---
description: rules to follow when interacting with new dependencies in this project. anything to do with package.json
globs: 
alwaysApply: false
---
 You are an expert developer using <PERSON><PERSON> as the package manager for JavaScript/TypeScript projects. Your task is to ensure all dependency management follows Bun best practices.

### Objective
- Use Bun exclusively for all package management operations, replacing npm, yarn, or pnpm.
- Leverage Bun's speed and efficiency for improved development workflow.

### Package Management Guidelines
- Always use `bun install` instead of `npm install` or `yarn add` for adding dependencies.
- Use `bun add [package]` for adding production dependencies.
- Use `bun add -d [package]` for adding development dependencies.
- Use `bun remove [package]` for removing dependencies.
- Use `bun update` to update dependencies.
- Always commit the `bun.lockb` file to version control.


### Testing
- Use Bun's built-in test runner with `bun test`.
- Configure tests in `bunfig.toml` if needed.

### Performance Considerations
- Leverage Bun's fast installation times for CI/CD pipelines.
- Use Bun's built-in bundler for production builds when appropriate.
- Consider using <PERSON><PERSON>'s native APIs for file operations and HTTP requests.

### Project Setup
- Initialize new projects with `bun init` instead of `npm init`.
- For existing projects, migrate by:
  1. Installing Bun globally: `npm install -g bun`
  2. Running `bun install` to generate `bun.lockb`
  3. Removing `node_modules`, `package-lock.json`, `yarn.lock`, or `pnpm-lock.yaml`

### Version Management
- Specify exact versions for critical dependencies.
- Use `bun update --lockfile-only` to update the lockfile without installing.
- Run `bun install --frozen-lockfile` in CI environments.

### Environment and Configuration
- Use `bunfig.toml` for Bun-specific configurations.
- Set up `.env` files for environment variables that Bun automatically loads.

When implementing these guidelines, always consider the specific requirements of your project and adapt accordingly. 