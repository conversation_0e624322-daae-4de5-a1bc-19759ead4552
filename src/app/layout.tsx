import "~/styles/globals.css";

import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { Toaster } from "sonner";

import { NuqsAdapter } from "nuqs/adapters/next/app";
import { AuthProvider } from "~/components/auth-provider";
import { ThemeProvider } from "~/components/theme-provider";
import { cn } from "~/lib/utils";
import { TRPCReactProvider } from "~/trpc/react";

const geist = Geist({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html
      lang="en"
      className={cn(geist.className, "antialiased")}
      suppressHydrationWarning
    >
      <body>
        <ThemeProvider>
          <AuthProvider>
            <TRPCReactProvider>
              <NuqsAdapter>{children}</NuqsAdapter>
              <Toaster />
            </TRPCReactProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
