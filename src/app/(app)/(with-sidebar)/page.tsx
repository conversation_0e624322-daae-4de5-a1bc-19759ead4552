import { currentUser } from "@clerk/nextjs/server";
import { GlowingEffectDemo } from "~/components/glowing-demo";
import { QuickLinks } from "~/components/navigation/quick-links";

export default async function HomePage() {
  const user = await currentUser();

  return (
    <div className="flex h-full flex-col justify-between gap-4">
      {/* Compact Header Section */}
      <section className="space-y-4">
        <h2 className="text-3xl font-semibold tracking-tight">
          Welcome, {user?.fullName}
        </h2>
        <QuickLinks />
      </section>

      <section className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold">Resources & Guides</h2>
        </div>
        <GlowingEffectDemo />
      </section>
    </div>
  );
}
