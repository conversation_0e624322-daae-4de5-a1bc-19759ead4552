import { Skeleton } from "~/components/ui/skeleton";

export default function Loading() {
  return (
    <div className="flex flex-col space-y-6 p-4 md:p-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex w-xl flex-col space-y-2">
          <Skeleton className="h-10 w-48" />{" "}
          {/* "Insights" title placeholder */}
        </div>
        <div className="w-full md:w-auto">
          <Skeleton className="h-12 w-full md:w-56" />{" "}
          {/* "Explore benchmark data" button placeholder */}
        </div>
      </div>
      {/* DashboardSkeleton equivalent */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div
            key={i}
            className="bg-card text-card-foreground rounded-lg border shadow-sm"
          >
            <div className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
              <Skeleton className="h-5 w-[120px]" />
            </div>
            <div className="p-6 pt-0">
              <Skeleton className="h-8 w-[80px]" />
            </div>
          </div>
        ))}
      </div>
      <Skeleton className="h-64 w-full" />{" "}
      {/* Placeholder for TokenDistributionChart */}
      <Skeleton className="h-64 w-full" />{" "}
      {/* Placeholder for AreaUnlockScheduleChart */}
    </div>
  );
}
