"use client";

import { useEffect, useState } from "react";
import {
  Allocation,
  DetailedEntry,
} from "~/components/modeling/allocation-card";
import AllocationHeader from "~/components/modeling/allocation-header";
import AllocationPool from "~/components/modeling/allocation-pool";
import { useModelingStore } from "~/components/modeling/modeling-store";
import { Label } from "~/components/ui/label";
import { NumberInput } from "~/components/ui/number-input";

const CHART_COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
  "var(--chart-5)",
] as const;

// Get color for allocation based on index
function getColorForIndex(index: number): string {
  const color = CHART_COLORS[index % CHART_COLORS.length] || CHART_COLORS[0];
  return color;
}

function generateInitialAllocations(): Allocation[] {
  return [
    {
      id: "employees",
      name: "Employees",
      amount: 15_000_000,
      percentage: 15,
      color: getColorForIndex(0),
      pool: "insiders",
      isExpanded: false,
      detailedEntries: [],
      isFixed: true,
    },
    {
      id: "founders",
      name: "Founders",
      amount: 20_000_000,
      percentage: 20,
      color: getColorForIndex(1),
      pool: "insiders",
      isExpanded: false,
      detailedEntries: [],
      isFixed: true,
    },
    {
      id: "advisors",
      name: "Advisors",
      amount: 5_000_000,
      percentage: 5,
      color: getColorForIndex(2),
      pool: "insiders",
      isExpanded: false,
      detailedEntries: [],
      isFixed: true,
    },
    {
      id: "investors",
      name: "Investors",
      amount: 25_000_000,
      percentage: 25,
      color: getColorForIndex(3),
      pool: "insiders",
      isExpanded: false,
      detailedEntries: [],
      isFixed: true,
    },
    {
      id: "treasury",
      name: "Treasury",
      amount: 20_000_000,
      percentage: 20,
      color: getColorForIndex(4),
      pool: "outsiders",
      isExpanded: false,
      detailedEntries: [],
      isFixed: true,
    },
    {
      id: "community",
      name: "Community",
      amount: 15_000_000,
      percentage: 15,
      color: getColorForIndex(5),
      pool: "outsiders",
      isExpanded: false,
      detailedEntries: [],
      isFixed: true,
    },
  ];
}

export default function ModelingPage() {
  const totalSupply = useModelingStore((state) => state.totalSupply);
  const setTotalSupply = useModelingStore((state) => state.setTotalSupply);

  const [allocations, setAllocations] = useState<Allocation[]>(
    generateInitialAllocations()
  );
  const setAllocatedAmount = useModelingStore(
    (state) => state.setAllocatedAmount
  );

  // Calculate total allocated amount and percentage
  useEffect(() => {
    const total = allocations.reduce(
      (sum, allocation) => sum + allocation.amount,
      0
    );
    setAllocatedAmount(total);
  }, [allocations]);

  // Update allocation when amount or percentage changes
  const updateAllocation = (
    id: string,
    field: "amount" | "percentage",
    value: number
  ) => {
    setAllocations((prev) => {
      return prev.map((allocation) => {
        if (allocation.id === id) {
          return field === "amount"
            ? {
                ...allocation,
                amount: value,
                percentage: totalSupply ? (value / totalSupply) * 100 : 0,
              }
            : {
                ...allocation,
                percentage: value,
                amount: totalSupply ? (value / 100) * totalSupply : 0,
              };
        }
        return allocation;
      });
    });
  };

  // Add new allocation
  const addAllocation = (pool: "insiders" | "outsiders") => {
    const newId = `allocation-${Date.now()}`;
    const poolAllocations = allocations.filter((a) => a.pool === pool);
    const colorIndex = poolAllocations.length % 10;

    setAllocations([
      ...allocations,
      {
        id: newId,
        name: "New Allocation",
        amount: 0,
        percentage: 0,
        color: getColorForIndex(colorIndex),
        pool,
        isExpanded: false,
        detailedEntries: [],
      },
    ]);
  };

  // Remove allocation
  const removeAllocation = (id: string) => {
    const allocation = allocations.find((a) => a.id === id);
    if (allocation?.isFixed) {
      return; // Prevent removing fixed allocations
    }
    setAllocations((prev) => prev.filter((a) => a.id !== id));
  };

  // Toggle allocation expansion
  const toggleAllocationExpansion = (id: string) => {
    setAllocations((prev) =>
      prev.map((allocation) =>
        allocation.id === id
          ? { ...allocation, isExpanded: !allocation.isExpanded }
          : allocation
      )
    );
  };

  // Update allocation name
  const updateAllocationName = (id: string, name: string) => {
    const allocation = allocations.find((a) => a.id === id);
    if (allocation?.isFixed) {
      return; // Prevent renaming fixed allocations
    }
    setAllocations((prev) =>
      prev.map((allocation) =>
        allocation.id === id ? { ...allocation, name } : allocation
      )
    );
  };

  // Add detailed entry to allocation
  const addDetailedEntry = (allocationId: string) => {
    setAllocations((prev) =>
      prev.map((allocation) => {
        if (allocation.id === allocationId) {
          const newEntryId = `entry-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`;
          return {
            ...allocation,
            detailedEntries: [
              ...allocation.detailedEntries,
              {
                id: newEntryId,
                title: "New Entry",
                equityPercentage: 0,
                tokenPercentage:
                  allocation.pool === "outsiders" ? 0 : undefined,
              },
            ],
          };
        }
        return allocation;
      })
    );
  };

  // Remove detailed entry from allocation
  const removeDetailedEntry = (allocationId: string, entryId: string) => {
    setAllocations((prev) =>
      prev.map((allocation) => {
        if (allocation.id === allocationId) {
          return {
            ...allocation,
            detailedEntries: allocation.detailedEntries.filter(
              (entry) => entry.id !== entryId
            ),
          };
        }
        return allocation;
      })
    );
  };

  // Update detailed entry
  const updateDetailedEntry = (
    allocationId: string,
    entryId: string,
    field: keyof DetailedEntry,
    value: string | number
  ) => {
    setAllocations((prev) =>
      prev.map((allocation) => {
        if (allocation.id === allocationId) {
          return {
            ...allocation,
            detailedEntries: allocation.detailedEntries.map((entry) => {
              if (entry.id === entryId) {
                return { ...entry, [field]: value };
              }
              return entry;
            }),
          };
        }
        return allocation;
      })
    );
  };

  // Sync detailed entries with allocation total
  const syncDetailedEntriesToAllocation = (allocationId: string) => {
    const allocation = allocations.find((a) => a.id === allocationId);
    if (!allocation) return;

    const totalDetailedPercentage = allocation.detailedEntries.reduce(
      (sum, entry) => sum + (entry.equityPercentage || 0),
      0
    );

    if (totalDetailedPercentage !== allocation.percentage) {
      updateAllocation(allocationId, "percentage", totalDetailedPercentage);
    }
  };

  // Distribute allocation percentage to detailed entries
  const distributeAllocationToEntries = (allocationId: string) => {
    const allocation = allocations.find((a) => a.id === allocationId);
    if (!allocation || allocation.detailedEntries.length === 0) return;

    const entryCount = allocation.detailedEntries.length;
    const percentagePerEntry = allocation.percentage / entryCount;

    setAllocations((prev) =>
      prev.map((a) => {
        if (a.id === allocationId) {
          return {
            ...a,
            detailedEntries: a.detailedEntries.map((entry) => ({
              ...entry,
              equityPercentage: percentagePerEntry,
              tokenPercentage:
                a.pool === "outsiders"
                  ? percentagePerEntry
                  : entry.tokenPercentage,
            })),
          };
        }
        return a;
      })
    );
  };

  // Calculate pool totals
  const insidersTotal = allocations
    .filter((a) => a.pool === "insiders")
    .reduce((sum, a) => sum + a.percentage, 0);

  const outsidersTotal = allocations
    .filter((a) => a.pool === "outsiders")
    .reduce((sum, a) => sum + a.percentage, 0);

  return (
    <main className="bg-background min-h-screen">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-semibold tracking-tight">Modeling</h2>
          <div className="flex items-center gap-2">
            <Label htmlFor="total-supply" className="whitespace-nowrap">
              Total Supply:
            </Label>
            <NumberInput
              id="total-supply"
              value={totalSupply}
              onChange={setTotalSupply}
            />
          </div>
        </div>
        <AllocationHeader allocations={allocations} />

        <AllocationPool
          title="Insiders Pool"
          pool="insiders"
          allocations={allocations.filter((a) => a.pool === "insiders")}
          totalPercentage={insidersTotal}
          onAddAllocation={() => addAllocation("insiders")}
          onRemoveAllocation={removeAllocation}
          onUpdateAllocation={updateAllocation}
          onUpdateAllocationName={updateAllocationName}
          onToggleExpansion={toggleAllocationExpansion}
          onAddDetailedEntry={addDetailedEntry}
          onRemoveDetailedEntry={removeDetailedEntry}
          onUpdateDetailedEntry={updateDetailedEntry}
          onSyncEntries={syncDetailedEntriesToAllocation}
          onDistributeAllocation={distributeAllocationToEntries}
        />

        <AllocationPool
          title="Outsiders Pool"
          pool="outsiders"
          allocations={allocations.filter((a) => a.pool === "outsiders")}
          totalPercentage={outsidersTotal}
          onAddAllocation={() => addAllocation("outsiders")}
          onRemoveAllocation={removeAllocation}
          onUpdateAllocation={updateAllocation}
          onUpdateAllocationName={updateAllocationName}
          onToggleExpansion={toggleAllocationExpansion}
          onAddDetailedEntry={addDetailedEntry}
          onRemoveDetailedEntry={removeDetailedEntry}
          onUpdateDetailedEntry={updateDetailedEntry}
          onSyncEntries={syncDetailedEntriesToAllocation}
          onDistributeAllocation={distributeAllocationToEntries}
        />
      </div>
    </main>
  );
}
