import { Skeleton } from "~/components/ui/skeleton";

export default function Loading() {
  return (
    <main className="bg-background min-h-screen p-4 md:p-8">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-1/3" />{" "}
          {/* "Modeling" title placeholder */}
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-24" />{" "}
            {/* "Total Supply:" label placeholder */}
            <Skeleton className="h-10 w-32" /> {/* NumberInput placeholder */}
          </div>
        </div>
        {/* AllocationHeader Placeholder */}
        <div className="flex items-center space-x-4 rounded-md border p-4">
          <Skeleton className="h-8 w-1/4" />
          <Skeleton className="h-8 w-1/4" />
          <Skeleton className="h-8 w-1/4" />
          <Skeleton className="h-8 w-1/4" />
        </div>

        {/* AllocationPool Placeholder (simplified) */}
        <div className="bg-card text-card-foreground rounded-lg border shadow-sm">
          <div className="flex flex-col space-y-1.5 p-6">
            <Skeleton className="h-6 w-1/2" /> {/* Pool title placeholder */}
            <Skeleton className="h-4 w-1/4" />{" "}
            {/* Total percentage placeholder */}
          </div>
          <div className="space-y-4 p-6 pt-0">
            {/* Placeholder for a few allocation entries */}
            <div className="space-y-2 border-b pb-2">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="space-y-2 border-b pb-2">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <Skeleton className="h-10 w-32" />{" "}
            {/* "Add Allocation" button placeholder */}
          </div>
        </div>
      </div>
    </main>
  );
}
