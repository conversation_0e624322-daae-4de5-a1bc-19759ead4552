"use client";

import { useUser } from "@clerk/nextjs";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  CameraIcon,
  CheckIcon,
  CopyIcon,
  PencilIcon,
  UserIcon,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { OrganizationMemberList } from "~/components/organization/organization-member-list";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "~/components/ui/tabs";
import { cn } from "~/lib/utils";

// Name editing modal schema
const nameEditSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
});

type NameEditFormValues = z.infer<typeof nameEditSchema>;

export default function ProfilePage() {
  const { user, isLoaded } = useUser();
  const [copied, setCopied] = useState(false);
  const [activeTab, setActiveTab] = useState("account");
  const [isNameModalOpen, setIsNameModalOpen] = useState(false);
  const [isUpdatingName, setIsUpdatingName] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  // Form for name editing modal
  const nameForm = useForm<NameEditFormValues>({
    resolver: zodResolver(nameEditSchema),
    defaultValues: {
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
    },
  });

  // Handle URL hash changes and set initial tab
  useEffect(() => {
    const getTabFromHash = () => {
      const hash = globalThis.location.hash.replace("#", "");
      const validTabs = ["account", "organization"];
      return validTabs.includes(hash) ? hash : "account";
    };

    // Set initial tab from URL hash
    setActiveTab(getTabFromHash());

    // Listen for hash changes
    const handleHashChange = () => {
      setActiveTab(getTabFromHash());
    };

    globalThis.addEventListener("hashchange", handleHashChange);
    return () => globalThis.removeEventListener("hashchange", handleHashChange);
  }, []);

  // Update URL hash when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    globalThis.history.pushState(null, "", `#${value}`);
  };

  const onNameSubmit = async (data: NameEditFormValues) => {
    setIsUpdatingName(true);
    try {
      await user?.update({
        firstName: data.firstName,
        lastName: data.lastName,
      });
      const resp = await user?.reload(); // Refresh user data to get the latest information
      nameForm.reset({
        firstName: resp?.firstName || "",
        lastName: resp?.lastName || "",
      });
      toast.success("Name updated successfully!");
      setIsNameModalOpen(false);
    } catch (error) {
      toast.error("Failed to update name");
      console.error("Error updating name:", error);
    } finally {
      setIsUpdatingName(false);
    }
  };

  const handleProfileImageUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast.error("Please upload an image file");
        return;
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File size must be less than 5MB");
        return;
      }

      setIsUploadingImage(true);
      try {
        await user?.setProfileImage({ file });
        await user?.reload(); // Refresh user data to get the latest image
        toast.success("Profile picture updated successfully!");
      } catch (error) {
        toast.error("Failed to upload profile picture");
        console.error("Error uploading profile picture:", error);
      } finally {
        setIsUploadingImage(false);
      }

      // Reset the input value so the same file can be selected again
      event.target.value = "";
    },
    [user]
  );

  const copyUserId = async () => {
    if (!user?.id) return;

    try {
      await navigator.clipboard.writeText(user.id);
      setCopied(true);
      toast.success("User ID copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error("Failed to copy User ID");
      console.error("Error copying to clipboard:", error);
    }
  };

  if (!isLoaded) {
    return (
      <div className="space-y-6">
        <h2 className="text-3xl font-semibold tracking-tight">Settings</h2>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-semibold tracking-tight">Settings</h2>

      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList variant="default">
          <TabsTrigger value="account" variant="default">
            Account
          </TabsTrigger>
          <TabsTrigger value="organization" variant="default">
            Organization
          </TabsTrigger>
        </TabsList>

        <TabsContent value="account" className="mt-6 space-y-6">
          {/* Header Section */}
          <div>
            <h3 className="text-2xl font-semibold">
              {user?.fullName || "User"}
            </h3>
            <p className="text-muted-foreground">
              {user?.emailAddresses[0]?.emailAddress}
            </p>
          </div>

          {/* Profile Picture Section */}
          <div className="flex items-center gap-4">
            <div className="relative">
              <Avatar className="h-20 w-20">
                <AvatarImage
                  src={user?.imageUrl}
                  alt={user?.fullName || "Profile"}
                />
                <AvatarFallback className="text-xl">
                  {user?.fullName ? (
                    user.fullName
                      .split(" ")
                      .map((n) => n[0])
                      .join("")
                      .toUpperCase()
                  ) : (
                    <UserIcon className="h-8 w-8" />
                  )}
                </AvatarFallback>
              </Avatar>
              {isUploadingImage && (
                <div className="absolute inset-0 flex items-center justify-center rounded-full bg-black/50">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-white border-t-transparent" />
                </div>
              )}
              <input
                type="file"
                accept="image/*"
                onChange={handleProfileImageUpload}
                className="hidden"
                id="profile-image-upload"
              />
              <label
                htmlFor="profile-image-upload"
                className={cn(
                  "bg-primary absolute -right-1 -bottom-1 flex h-6 w-6 cursor-pointer items-center justify-center rounded-full transition-opacity",
                  isUploadingImage && "pointer-events-none opacity-50"
                )}
              >
                <CameraIcon className="text-primary-foreground h-3 w-3" />
              </label>
            </div>
          </div>

          {/* Account Information Form */}
          <Card className="p-0 py-1">
            <CardContent className="p-0">
              <div className="divide-border space-y-0 divide-y">
                {/* Name Row */}
                <div className="flex items-center justify-between p-4">
                  <span className="text-foreground w-[300px] text-sm font-medium">
                    Name
                  </span>
                  <div className="flex flex-1 items-center justify-between gap-2">
                    <span className="text-muted-foreground text-sm">
                      {user?.fullName}
                    </span>
                    <Dialog
                      open={isNameModalOpen}
                      onOpenChange={setIsNameModalOpen}
                    >
                      <DialogTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <PencilIcon className="h-3 w-3" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                          <DialogTitle>Edit Name</DialogTitle>
                        </DialogHeader>
                        <Form {...nameForm}>
                          <form
                            onSubmit={nameForm.handleSubmit(onNameSubmit)}
                            className="space-y-4"
                          >
                            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                              <FormField
                                control={nameForm.control}
                                name="firstName"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>First Name</FormLabel>
                                    <FormControl>
                                      <Input
                                        placeholder="Enter your first name"
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={nameForm.control}
                                name="lastName"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Last Name</FormLabel>
                                    <FormControl>
                                      <Input
                                        placeholder="Enter your last name"
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                            <DialogFooter>
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsNameModalOpen(false)}
                              >
                                Cancel
                              </Button>
                              <Button
                                type="submit"
                                disabled={
                                  isUpdatingName || !nameForm.formState.isDirty
                                }
                              >
                                {isUpdatingName ? "Saving..." : "Save"}
                              </Button>
                            </DialogFooter>
                          </form>
                        </Form>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
                <div className="flex items-center justify-between p-4">
                  <span className="text-foreground w-[300px] text-sm font-medium">
                    Email
                  </span>
                  <span className="text-muted-foreground flex-1 text-sm">
                    {user?.emailAddresses[0]?.emailAddress}
                  </span>
                </div>
                <div className="flex items-center justify-between p-4">
                  <span className="text-foreground w-[300px] text-sm font-medium">
                    User ID
                  </span>
                  <div className="flex flex-1 items-center justify-between gap-2">
                    <span className="text-muted-foreground font-mono text-sm">
                      {user?.id}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={copyUserId}
                      className="h-6 px-2"
                    >
                      {copied ? (
                        <CheckIcon className="h-3 w-3" />
                      ) : (
                        <CopyIcon className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="organization" className="mt-6 space-y-6">
          <OrganizationMemberList />
        </TabsContent>
      </Tabs>
    </div>
  );
}
