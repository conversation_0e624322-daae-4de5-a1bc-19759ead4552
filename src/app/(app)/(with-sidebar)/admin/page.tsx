"use client";

import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { CreateOrganizationDialog } from "~/components/admin/create-organization-dialog";
import { OrganizationsTable } from "~/components/admin/organizations-table";
import { UsersTable } from "~/components/admin/users-table";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Progress } from "~/components/ui/progress";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { api } from "~/trpc/react";
import {
  DatabaseIcon,
  FileTextIcon,
  UploadIcon,
  XIcon,
  Loader2Icon,
} from "lucide-react";

// Helper functions moved to outer scope
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return (
    Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  );
};

const validateJsonFile = async (
  file: File
): Promise<{ valid: boolean; preview?: string; error?: string }> => {
  try {
    const content = await file.text();
    const parsed = JSON.parse(content);
    const preview = JSON.stringify(parsed, null, 2).slice(0, 500) + "...";
    return { valid: true, preview };
  } catch {
    return { valid: false, error: "Invalid JSON format" };
  }
};

export default function AdminPage() {
  const [userSearchQuery, setUserSearchQuery] = useState("");
  const [orgSearchQuery, setOrgSearchQuery] = useState("");

  // Single file upload state
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [projectName, setProjectName] = useState("");
  const [projectSymbol, setProjectSymbol] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedDataType, setSelectedDataType] = useState<
    "public_project_data" | "private_project_data"
  >("public_project_data");
  const [jsonPreview, setJsonPreview] = useState<string>("");

  // tRPC mutation for uploading project data
  const uploadProjectMutation = api.admin.uploadProjectData.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      // Reset form after successful upload
      setSelectedFile(null);
      setProjectName("");
      setProjectSymbol("");
      setJsonPreview("");
      setUploadProgress(0);
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  // Handle single file selection
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0]!; // Take only the first file
    const validation = await validateJsonFile(file);

    if (!validation.valid) {
      toast.error("Invalid JSON file", {
        description: validation.error,
      });
      return;
    }

    setSelectedFile(file);
    setJsonPreview(validation.preview || "");

    // Auto-generate project name and symbol from filename
    const baseName = file.name.replace(/\.json$/i, "");
    setProjectName(baseName.replaceAll(/[-_]/g, " "));
    setProjectSymbol(baseName.toUpperCase().slice(0, 10));
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/json": [".json"],
      "text/plain": [".json"],
    },
    multiple: false, // Single file only
    maxFiles: 1,
  });

  // Clear selected file
  const clearFile = () => {
    setSelectedFile(null);
    setProjectName("");
    setProjectSymbol("");
    setJsonPreview("");
  };

  // Upload project data to database
  const uploadProject = async () => {
    if (!selectedFile || !projectName.trim() || !projectSymbol.trim()) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Read the file content
      const fileContent = await selectedFile.text();
      const jsonData = JSON.parse(fileContent);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev < 90) return prev + 10;
          return prev;
        });
      }, 100);

      // Upload using tRPC mutation
      await uploadProjectMutation.mutateAsync({
        fileName: selectedFile.name,
        projectName: projectName.trim(),
        projectSymbol: projectSymbol.trim().toUpperCase(),
        jsonData,
        dataType: selectedDataType,
      });

      // Clear progress interval
      clearInterval(progressInterval);
      setUploadProgress(100);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Upload failed");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="flex h-full w-full flex-col gap-4">
      <h2 className="text-3xl font-semibold tracking-tight">Admin</h2>

      <Tabs defaultValue="admin" className="flex flex-1 flex-col">
        <TabsList className="w-fit">
          <TabsTrigger value="admin">Admin</TabsTrigger>
          <TabsTrigger value="developer">Developer</TabsTrigger>
        </TabsList>

        <TabsContent value="admin" className="mt-4 flex-1">
          <div className="space-y-8">
            {/* Organizations Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Organizations</h3>
                <CreateOrganizationDialog />
              </div>

              <OrganizationsTable
                searchQuery={orgSearchQuery}
                onSearchChange={setOrgSearchQuery}
              />
            </div>

            {/* Users Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Users</h3>
              </div>

              {/* Search Input */}
              <div className="flex items-center gap-2">
                <Input
                  placeholder="Search users"
                  value={userSearchQuery}
                  onChange={(e) => setUserSearchQuery(e.target.value)}
                  className="h-9 w-[300px]"
                />
              </div>

              <UsersTable
                searchQuery={userSearchQuery}
                onSearchChange={setUserSearchQuery}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="developer" className="mt-6 flex-1">
          <div className="space-y-6">
            {/* Header */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium">Project Data Import</h3>
                <p className="text-muted-foreground text-sm">
                  Upload a single JSON file to import project data into the
                  database. Specify the project name and symbol for the new
                  project.
                </p>
              </div>

              {/* Data Type Selector */}
              <div className="flex items-center gap-4">
                <Label htmlFor="dataType" className="text-sm font-medium">
                  Data Type:
                </Label>
                <Select
                  value={selectedDataType}
                  onValueChange={(value) =>
                    setSelectedDataType(
                      value as "public_project_data" | "private_project_data"
                    )
                  }
                >
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select data type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public_project_data">
                      Public Project Data
                    </SelectItem>
                    <SelectItem value="private_project_data">
                      Private Project Data
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Upload Area */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Upload Project File</CardTitle>
                <CardDescription>
                  Select a JSON file containing project allocation data
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedFile ? (
                  <div className="space-y-4">
                    {/* Selected File Display */}
                    <div className="flex items-center justify-between rounded-lg border p-4">
                      <div className="flex items-center gap-3">
                        <div className="bg-muted rounded p-2">
                          <FileTextIcon className="text-muted-foreground h-5 w-5" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">
                            {selectedFile.name}
                          </p>
                          <p className="text-muted-foreground text-xs">
                            {formatFileSize(selectedFile.size)}
                          </p>
                        </div>
                      </div>
                      <Button
                        onClick={clearFile}
                        variant="ghost"
                        size="sm"
                        disabled={isUploading}
                      >
                        <XIcon className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Project Details Form */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="projectName">Project Name</Label>
                        <Input
                          id="projectName"
                          placeholder="Enter project name"
                          value={projectName}
                          onChange={(e) => setProjectName(e.target.value)}
                          disabled={isUploading}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="projectSymbol">Project Symbol</Label>
                        <Input
                          id="projectSymbol"
                          placeholder="Enter symbol (e.g., BTC)"
                          value={projectSymbol}
                          onChange={(e) =>
                            setProjectSymbol(e.target.value.toUpperCase())
                          }
                          maxLength={10}
                          disabled={isUploading}
                        />
                      </div>
                    </div>

                    {/* JSON Preview */}
                    {jsonPreview && (
                      <div className="space-y-2">
                        <Label>JSON Preview</Label>
                        <div className="bg-muted max-h-32 overflow-y-auto rounded p-3">
                          <pre className="text-muted-foreground text-xs">
                            {jsonPreview}
                          </pre>
                        </div>
                      </div>
                    )}

                    {/* Upload Progress */}
                    {isUploading && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label>Upload Progress</Label>
                          <span className="text-muted-foreground text-sm">
                            {uploadProgress}%
                          </span>
                        </div>
                        <Progress value={uploadProgress} className="h-2" />
                      </div>
                    )}

                    {/* Upload Button */}
                    <div className="flex justify-end">
                      <Button
                        onClick={uploadProject}
                        disabled={
                          isUploading ||
                          !selectedFile ||
                          !projectName.trim() ||
                          !projectSymbol.trim()
                        }
                        className="min-w-32"
                      >
                        {isUploading ? (
                          <>
                            <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                            Uploading...
                          </>
                        ) : (
                          <>
                            <DatabaseIcon className="mr-2 h-4 w-4" />
                            Import Project
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div
                    {...getRootProps()}
                    className={`cursor-pointer rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
                      isDragActive
                        ? "border-primary bg-primary/5"
                        : "border-muted-foreground/25 hover:border-muted-foreground/50"
                    }`}
                  >
                    <input {...getInputProps()} />
                    <div className="flex flex-col items-center gap-4">
                      <div className="bg-muted rounded-full p-3">
                        <DatabaseIcon className="text-muted-foreground h-6 w-6" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium">
                          {isDragActive
                            ? "Drop JSON file here"
                            : "Upload JSON file"}
                        </h4>
                        <p className="text-muted-foreground mt-1 text-xs">
                          Drag and drop a JSON file or click to browse
                        </p>
                      </div>
                      <Button variant="outline" size="sm" type="button">
                        <UploadIcon className="mr-2 h-4 w-4" />
                        Select File
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
