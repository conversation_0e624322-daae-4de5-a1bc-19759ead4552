import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import { ScatterCustomizedShape } from "recharts/types/cartesian/Scatter";
import { RouterOutputs } from "~/trpc/react";

export type Allocation = NonNullable<
  RouterOutputs["allocation"]["getAll"]
>[number];

interface AllocationDetailViewProps {
  allocation: Allocation;
}

type ScatterDataPoint = {
  equity: number;
  token: number;
  tokenToEquityRatio: number;
  title: string;
};

interface RenderCustomizedShapeProps {
  cx: number;
  cy: number;
  fill: string;
  payload: ScatterDataPoint;
}

const renderCustomizedShape = (props: RenderCustomizedShapeProps) => {
  const { cx, cy, fill } = props;
  if (!cx || !cy) return <div />;

  return (
    <circle
      cx={cx}
      cy={cy}
      r={7}
      fill={fill}
      stroke="var(--primary-foreground)"
      strokeWidth={2}
      strokeOpacity={0.5}
      style={{
        transition: "all 0.2s ease-in-out",
        cursor: "pointer",
      }}
      onMouseEnter={(e) => {
        const target = e.target as SVGCircleElement;
        target.setAttribute("r", "10");
      }}
      onMouseLeave={(e) => {
        const target = e.target as SVGCircleElement;
        target.setAttribute("r", "7");
      }}
    />
  );
};

export const AllocationDetailView = ({
  allocation: selectedAllocation,
}: AllocationDetailViewProps) => {
  // Transform the data for the scatter plot
  const scatterData = selectedAllocation.allocations
    .map((alloc) => {
      const equityDecimal = Number(alloc.equityDecimal);
      const tokenDecimal = Number(alloc.totalTokenDecimal);

      // Skip invalid or zero values
      if (
        Number.isNaN(equityDecimal) ||
        Number.isNaN(tokenDecimal) ||
        equityDecimal === 0 ||
        tokenDecimal === 0
      ) {
        return null;
      }

      return {
        equity: equityDecimal * 100,
        token: tokenDecimal * 100,
        tokenToEquityRatio: tokenDecimal / equityDecimal,
        title: alloc.title || alloc.group,
      };
    })
    .filter((data): data is NonNullable<typeof data> => data !== null);

  // Calculate summary statistics
  const totalAllocations = scatterData.length;
  const averageEquity =
    totalAllocations > 0
      ? scatterData.reduce((sum, data) => sum + data.equity, 0) /
        totalAllocations
      : 0;
  const averageToken =
    totalAllocations > 0
      ? scatterData.reduce((sum, data) => sum + data.token, 0) /
        totalAllocations
      : 0;
  const averageRatio =
    totalAllocations > 0
      ? scatterData.reduce((sum, data) => sum + data.tokenToEquityRatio, 0) /
        totalAllocations
      : 0;

  return (
    <div className="space-y-6">
      <div className="h-[400px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
            <CartesianGrid strokeDasharray="5 5" stroke="var(--muted)" />

            <XAxis
              type="number"
              dataKey="equity"
              name="Equity Decimal"
              axisLine={false}
              tickLine={false}
              fontSize={12}
              tickFormatter={(value) => {
                // Only show % for non-zero values on x-axis
                if (value === 0) return "0%";
                return `${value}%`;
              }}
              stroke="var(--muted-foreground)"
            />
            <YAxis
              type="number"
              dataKey="token"
              name="Token/Equity Ratio"
              axisLine={false}
              tickLine={false}
              fontSize={12}
              tickFormatter={(value) => {
                // Show 0% only on y-axis at origin
                if (value === 0) return "";
                return `${value}%`;
              }}
              stroke="var(--muted-foreground)"
            />
            <Tooltip
              cursor={{
                strokeDasharray: "5 5",
                stroke: "var(--muted-foreground)",
              }}
              content={({ active, payload }) => {
                if (active && payload?.[0]?.payload) {
                  const data = payload[0].payload as ScatterDataPoint;
                  return (
                    <div className="bg-secondary w-[150px] space-y-1 rounded-md px-3 py-2 text-sm shadow-xl">
                      <div className="text-secondary-foreground flex justify-between">
                        <span>Equity</span>
                        <span>
                          {data.equity.toLocaleString(undefined, {
                            maximumSignificantDigits: 3,
                          })}
                          %
                        </span>
                      </div>
                      <div className="text-secondary-foreground flex justify-between">
                        <span>Token</span>
                        <span>
                          {data.token.toLocaleString(undefined, {
                            maximumSignificantDigits: 3,
                          })}
                          %
                        </span>
                      </div>
                      <div className="text-secondary-foreground flex justify-between">
                        <span>Ratio</span>
                        <span>
                          {data.tokenToEquityRatio.toLocaleString(undefined, {
                            maximumSignificantDigits: 3,
                          })}
                        </span>
                      </div>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Scatter
              name="Benchmarks"
              data={scatterData}
              fill="var(--primary)"
              shape={renderCustomizedShape as ScatterCustomizedShape}
            />
          </ScatterChart>
        </ResponsiveContainer>
      </div>

      <div className="grid grid-cols-1 items-center justify-items-center gap-x-6 gap-y-2 rounded-md border p-4 text-sm md:grid-cols-2 lg:grid-cols-4">
        <div>
          <span className="text-muted-foreground">Total Benchmarks: </span>
          <span className="font-semibold">{totalAllocations}</span>
        </div>
        <div>
          <span className="text-muted-foreground">Avg Equity %: </span>
          <span className="font-semibold">
            {averageEquity.toLocaleString(undefined, {
              maximumSignificantDigits: 3,
            })}
            %
          </span>
        </div>
        <div>
          <span className="text-muted-foreground">Avg Token %: </span>
          <span className="font-semibold">
            {averageToken.toLocaleString(undefined, {
              maximumSignificantDigits: 3,
            })}
            %
          </span>
        </div>
        <div>
          <span className="text-muted-foreground">Avg Ratio: </span>
          <span className="font-semibold">
            {averageRatio.toLocaleString(undefined, {
              maximumSignificantDigits: 3,
            })}
          </span>
        </div>
      </div>
    </div>
  );
};
