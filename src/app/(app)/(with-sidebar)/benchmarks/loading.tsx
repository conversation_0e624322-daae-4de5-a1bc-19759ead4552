import { Skeleton } from "~/components/ui/skeleton";

export default function Loading() {
  return (
    <div className="flex h-[calc(100vh-3.5rem)] flex-col p-4 md:p-8">
      <div className="mb-4 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
        <Skeleton className="h-10 w-1/3" />{" "}
        {/* "Benchmarks" title placeholder */}
        <div className="flex w-full flex-col items-stretch gap-2 md:w-auto md:flex-row md:items-center">
          <Skeleton className="h-10 w-full md:w-64" />{" "}
          {/* Search input placeholder */}
          <Skeleton className="h-10 w-full md:w-auto" />{" "}
          {/* Button placeholder */}
          <Skeleton className="h-10 w-full md:w-auto" />{" "}
          {/* Button placeholder */}
        </div>
      </div>

      {/* AllocationTable Placeholder */}
      <div className="flex-1 overflow-auto rounded-md border">
        {/* Table Header */}
        <div className="flex border-b p-4">
          <Skeleton className="h-6 w-1/4" />
          <Skeleton className="h-6 w-1/4" />
          <Skeleton className="h-6 w-1/4" />
          <Skeleton className="h-6 w-1/4" />
        </div>
        {/* Table Body - چند Skeleton rows */}
        <div className="space-y-2 p-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex space-x-2">
              <Skeleton className="h-8 w-1/4" />
              <Skeleton className="h-8 w-1/4" />
              <Skeleton className="h-8 w-1/4" />
              <Skeleton className="h-8 w-1/4" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
