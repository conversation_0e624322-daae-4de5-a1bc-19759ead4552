"use client";

import { Table } from "@tanstack/react-table";
import { CheckIcon, ChevronDownIcon, RotateCcwIcon, XIcon } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { ColumnVisibilityDropdown } from "~/components/tables/column-visibility-dropdown";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Command, CommandGroup, CommandItem } from "~/components/ui/command";
import { Input } from "~/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { GROUP_TYPES, INDUSTRIES, STAKEHOLDER_TYPES } from "~/lib/constants";
import { cn } from "~/lib/utils";

interface Props<TData> {
  table: Table<TData>;
  selectedUserTypes: string[];
  setSelectedUserTypes: (value: string[]) => void;
  selectedUserGroups: string[];
  setSelectedUserGroups: (value: string[]) => void;
  selectedIndustries: string[];
  setSelectedIndustries: (value: string[]) => void;
  searchQuery: string;
  setSearchQuery: (value: string) => void;
}

export function AllocationTableFilters<TData>({
  table,
  selectedUserTypes,
  setSelectedUserTypes,
  selectedUserGroups,
  setSelectedUserGroups,
  selectedIndustries,
  setSelectedIndustries,
  searchQuery,
  setSearchQuery,
}: Props<TData>) {
  const hasFilters =
    selectedUserTypes.length > 0 ||
    selectedUserGroups.length > 0 ||
    selectedIndustries.length > 0;
  return (
    <div className="flex flex-col gap-2 p-1">
      <div className="space-between flex">
        <div className="flex items-center gap-2">
          <Input
            placeholder="Search..."
            value={searchQuery}
            onChange={(event) => setSearchQuery(event.target.value)}
            className="h-9 w-[150px] lg:w-[250px]"
          />
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                className="justify-between"
              >
                User Type
                <ChevronDownIcon className="ml-2 h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0" align="start">
              <Command>
                <CommandGroup>
                  {STAKEHOLDER_TYPES.map((userType) => (
                    <CommandItem
                      key={userType}
                      onSelect={() => {
                        const isSelected = selectedUserTypes.includes(userType);
                        setSelectedUserTypes(
                          isSelected
                            ? selectedUserTypes.filter((t) => t !== userType)
                            : [...selectedUserTypes, userType]
                        );
                      }}
                      className="flex items-center justify-between"
                    >
                      {userType}
                      <CheckIcon
                        className={cn(
                          "size-4",
                          selectedUserTypes.includes(userType)
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                className="justify-between"
              >
                Group
                <ChevronDownIcon className="ml-2 h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0" align="start">
              <Command>
                <CommandGroup>
                  {GROUP_TYPES.map((userGroup) => (
                    <CommandItem
                      key={userGroup}
                      onSelect={() => {
                        const isSelected =
                          selectedUserGroups.includes(userGroup);
                        setSelectedUserGroups(
                          isSelected
                            ? selectedUserGroups.filter((t) => t !== userGroup)
                            : [...selectedUserGroups, userGroup]
                        );
                      }}
                      className="flex items-center justify-between"
                    >
                      {userGroup}
                      <CheckIcon
                        className={cn(
                          "size-4",
                          selectedUserGroups.includes(userGroup)
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                className="justify-between"
              >
                Industry
                <ChevronDownIcon className="ml-2 h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0" align="start">
              <Command>
                <CommandGroup>
                  {INDUSTRIES.map((industry) => (
                    <CommandItem
                      key={industry.value}
                      onSelect={() => {
                        const isSelected = selectedIndustries.includes(
                          industry.value
                        );
                        setSelectedIndustries(
                          isSelected
                            ? selectedIndustries.filter(
                                (i) => i !== industry.value
                              )
                            : [...selectedIndustries, industry.value]
                        );
                      }}
                      className="flex items-center justify-between"
                    >
                      {industry.label}
                      <CheckIcon
                        className={cn(
                          "size-4",
                          selectedIndustries.includes(industry.value)
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
        <ColumnVisibilityDropdown table={table} />
      </div>

      <AnimatePresence>
        {hasFilters && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.1 }}
            className="overflow-hidden"
          >
            <div className="flex flex-wrap items-start gap-y-2">
              <div className="hidden w-full flex-wrap items-center gap-2 sm:flex">
                {/* Selected user type badges */}
                {selectedUserTypes.length > 0 && (
                  <div className="flex flex-wrap items-center gap-2">
                    <span className="text-muted-foreground pl-2 text-sm font-medium">
                      User Type
                    </span>
                    <AnimatePresence>
                      {selectedUserTypes.map((type) => (
                        <motion.div
                          key={type}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.8 }}
                          transition={{ duration: 0.15 }}
                        >
                          <Badge
                            variant="primary"
                            size="large"
                            className="items-center gap-1"
                          >
                            {type}
                            <XIcon
                              className="h-3 w-3 cursor-pointer"
                              onClick={() => {
                                const filtered = selectedUserTypes.filter(
                                  (t) => t !== type
                                );
                                setSelectedUserTypes(filtered);
                              }}
                            />
                          </Badge>
                        </motion.div>
                      ))}
                    </AnimatePresence>
                  </div>
                )}

                {/* Selected user group badges */}
                {selectedUserGroups.length > 0 && (
                  <div className="flex flex-wrap items-center gap-2">
                    <span className="text-muted-foreground pl-2 text-sm font-medium">
                      User Group
                    </span>
                    <AnimatePresence>
                      {selectedUserGroups.map((group) => (
                        <motion.div
                          key={group}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.8 }}
                          transition={{ duration: 0.15 }}
                        >
                          <Badge
                            variant="primary"
                            size="large"
                            className="items-center gap-1"
                          >
                            {group}
                            <XIcon
                              className="h-3 w-3 cursor-pointer"
                              onClick={() => {
                                const filtered = selectedUserGroups.filter(
                                  (t) => t !== group
                                );
                                setSelectedUserGroups(filtered);
                              }}
                            />
                          </Badge>
                        </motion.div>
                      ))}
                    </AnimatePresence>
                  </div>
                )}

                {/* Selected industry badges */}
                {selectedIndustries.length > 0 && (
                  <div className="flex flex-wrap items-center gap-2">
                    <span className="text-muted-foreground pl-2 text-sm font-medium">
                      Industry
                    </span>
                    <AnimatePresence>
                      {selectedIndustries.map((industryValue) => {
                        const industryLabel =
                          INDUSTRIES.find((i) => i.value === industryValue)
                            ?.label ?? industryValue;
                        return (
                          <motion.div
                            key={industryValue}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            transition={{ duration: 0.15 }}
                          >
                            <Badge
                              variant="primary"
                              size="large"
                              className="items-center gap-1"
                            >
                              {industryLabel}
                              <XIcon
                                className="h-3 w-3 cursor-pointer"
                                onClick={() => {
                                  const filtered = selectedIndustries.filter(
                                    (i) => i !== industryValue
                                  );
                                  setSelectedIndustries(filtered);
                                }}
                              />
                            </Badge>
                          </motion.div>
                        );
                      })}
                    </AnimatePresence>
                  </div>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSelectedUserTypes([]);
                    setSelectedUserGroups([]);
                    setSelectedIndustries([]);
                  }}
                >
                  <RotateCcwIcon className="size-4" />
                  <span className="sm:inline">Reset</span>
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
