"use client";

import { isEqual } from "@react-hookz/deep-equal";
import { useDeepCompareMemo } from "@react-hookz/web";
import {
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
  type Row,
} from "@tanstack/react-table";
import { useQueryState } from "nuqs";
import React from "react";
import { DataTable } from "~/components/tables/data-table";
import { Skeleton } from "~/components/ui/skeleton";
import {
  INDUSTRY,
  SEARCH_QUERY,
  USER_GROUPS,
  USER_TYPES,
} from "~/lib/search-param-constants";
import { type RouterOutputs } from "~/trpc/react";
import { getAllocationTableColumns } from "./allocation-table-columns";
import { AllocationTableFilters } from "./allocation-table-filters";

type Allocation = NonNullable<RouterOutputs["allocation"]["getAll"]>[number];

interface AllocationTableProps {
  data: Allocation[];
  isLoading: boolean;
  onSelectAllocation: (allocation: Allocation) => void;
}

export const AllocationTable = ({
  data,
  isLoading,
  onSelectAllocation,
}: AllocationTableProps) => {
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({
      "project.industry": false,
      lockingDuration: false,
      vestingDuration: false,
      vestingType: false,
    });
  const [sorting, setSorting] = React.useState<SortingState>([]);

  const [selectedUserTypes, setSelectedUserTypes] = useQueryState<string[]>(
    USER_TYPES,
    {
      defaultValue: [],
      parse: (value) => value.split("&").filter(Boolean),
      serialize: (value) => value.join("&"),
      clearOnDefault: true,
      eq: isEqual,
    }
  );

  const [selectedUserGroups, setSelectedUserGroups] = useQueryState<string[]>(
    USER_GROUPS,
    {
      defaultValue: [],
      parse: (value) => value.split("&").filter(Boolean),
      serialize: (value) => value.join("&"),
      clearOnDefault: true,
      eq: isEqual,
    }
  );

  const [selectedIndustries, setSelectedIndustries] = useQueryState<string[]>(
    INDUSTRY,
    {
      defaultValue: [],
      parse: (value) => value.split("&").filter(Boolean),
      serialize: (value) => value.join("&"),
      clearOnDefault: true,
      eq: isEqual,
    }
  );

  const [searchQuery, setSearchQuery] = useQueryState(SEARCH_QUERY, {
    defaultValue: "",
    clearOnDefault: true,
  });

  const columnFilters = useDeepCompareMemo(() => {
    const filters = [];
    if (selectedUserTypes.length > 0) {
      filters.push({ id: "stakeholder_type", value: selectedUserTypes });
    }
    if (selectedUserGroups.length > 0) {
      filters.push({ id: "group", value: selectedUserGroups });
    }
    if (selectedIndustries.length > 0) {
      filters.push({ id: "project.industry", value: selectedIndustries });
    }

    return filters;
  }, [selectedUserTypes, selectedUserGroups, selectedIndustries]);

  const columns = React.useMemo(() => getAllocationTableColumns(), []);

  const table = useReactTable({
    data,
    columns,

    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onSortingChange: setSorting,
    onGlobalFilterChange: setSearchQuery,
    state: {
      columnVisibility,
      sorting,
      columnFilters,
      globalFilter: searchQuery,
    },
  });

  const handleRowClick = (row: Row<Allocation>) => {
    onSelectAllocation(row.original);
  };

  return (
    <div className="flex h-full flex-col gap-2">
      <AllocationTableFilters
        table={table}
        selectedUserTypes={selectedUserTypes}
        setSelectedUserTypes={setSelectedUserTypes}
        selectedUserGroups={selectedUserGroups}
        setSelectedUserGroups={setSelectedUserGroups}
        selectedIndustries={selectedIndustries}
        setSelectedIndustries={setSelectedIndustries}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
      />
      {isLoading ? (
        <AllocationTableSkeleton numColumns={columns.length} />
      ) : (
        <DataTable
          columns={columns}
          data={data}
          outputColumnStartIndex={3}
          hideToolbar
          table={table}
          onRowClick={handleRowClick}
          className="overflow-auto"
        />
      )}
    </div>
  );
};

function AllocationTableSkeleton({ numColumns }: { numColumns: number }) {
  const columns = Array.from({ length: numColumns }, (_, i) => i);
  return (
    <div className="rounded-md">
      <div className="bg-muted/50 h-10 rounded-md px-4">
        <div className="flex h-full items-center gap-4">
          {columns.map((column, index) => (
            <Skeleton key={index} className="h-4 w-full" />
          ))}
        </div>
      </div>
      <div className="mt-2 space-y-1">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="px-4 py-3">
            <div className="flex items-center gap-4">
              {columns.map((column, j) => (
                <Skeleton key={j} className="h-4 w-full" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
