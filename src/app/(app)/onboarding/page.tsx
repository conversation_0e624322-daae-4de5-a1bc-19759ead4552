import { auth, currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { OnboardingForm } from "~/components/onboarding/onboarding-form";

export default async function OnboardingPage() {
  // Fetch the current user from Clerk
  const user = await currentUser();
  const { redirectToSignIn } = await auth();

  if (!user) {
    return redirectToSignIn();
  }

  if (user.publicMetadata.onboardingCompleted) {
    redirect("/");
  }

  return (
    <div className="bg-background min-h-screen">
      <OnboardingForm
        user={{
          id: user.id,
          name: user.fullName,
        }}
      />
    </div>
  );
}
