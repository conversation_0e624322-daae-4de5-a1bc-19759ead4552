"use client";
import { useState } from "react";
import { BackgroundBeams } from "~/components/magicui/background-beams";
import { Input } from "~/components/ui/input";
import { SubmitButton } from "~/components/ui/submit-button";

const Page = (): React.ReactElement => {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      console.log("Email is required");
      return;
    }

    setIsSubmitting(true);

    try {
      // TODO: Implement actual email submission to your backend
      console.log("Submitting email:", email);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      console.log("Email submitted successfully");
      // You could show a success message or redirect here
    } catch (error) {
      console.error("Failed to submit email:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="relative flex h-screen w-full flex-col items-center justify-center bg-neutral-950 antialiased">
      <div className="mx-auto max-w-2xl p-4">
        <h1 className="text-primary-foreground/90 relative z-10 bg-clip-text text-center font-sans text-lg font-bold md:text-7xl">
          Contact Us
        </h1>

        <p className="text-muted-foreground text-md relative z-10 mx-auto my-4 max-w-lg text-center">
          To access this platform, you&apos;ll need to be part of an
          organization.
          <br />
          Our customer support team is here to help you get set up and connected
          with the right organization for your needs.
        </p>
        <form onSubmit={handleSubmit} className="relative z-10 flex gap-4">
          <Input
            placeholder="Email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isSubmitting}
            required
          />
          <SubmitButton isSubmitting={isSubmitting} disabled={!email}>
            Submit
          </SubmitButton>
        </form>
      </div>
      <BackgroundBeams />
    </div>
  );
};
export default Page;
