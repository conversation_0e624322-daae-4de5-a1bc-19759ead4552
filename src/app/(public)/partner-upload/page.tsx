"use client";

import FileUploader from "~/components/file-uploader";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";

export default function PartnerUploadPage() {
  return (
    <div className="bg-background min-h-screen">
      {/* Main content */}
      <main className="container mx-auto px-4 py-8">
        <div className="mx-auto max-w-4xl">
          <h1 className="mb-6 text-3xl font-bold tracking-tight">
            Partner File Upload
          </h1>

          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Upload Files</CardTitle>
              <CardDescription>
                Upload your files securely to our platform. We support CSV,
                XLSX, PDF, and ZIP files.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FileUploader />
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
