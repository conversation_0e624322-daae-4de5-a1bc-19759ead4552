import { env } from "~/env";

export async function GET(request: Request) {
  // Validate the CRON_SECRET
  const authHeader = request.headers.get("Authorization");
  const token = authHeader?.replace("Bearer ", "");

  if (env.NODE_ENV !== "development" && (!token || token !== env.CRON_SECRET)) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    // Your cron job logic here
    console.log("Running scheduled sync job");

    // Return a success response
    return new Response("Sync completed successfully", { status: 200 });
  } catch (error) {
    console.error("Error in sync job:", error);
    return new Response("Error in sync job", { status: 500 });
  }
}
