import { auth } from "@clerk/nextjs/server";
import { handleUpload, type HandleUploadBody } from "@vercel/blob/client";
import { NextResponse } from "next/server";
import { env } from "~/env";

export async function POST(request: Request): Promise<NextResponse> {
  try {
    // Authenticate the user
    const { userId, orgId } = await auth();
    if (!userId || !orgId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get organization ID (fallback to user ID if no org)
    const organizationId = orgId;

    const body = (await request.json()) as HandleUploadBody;

    const jsonResponse = await handleUpload({
      body,
      request,
      token: env.BLOB_READ_WRITE_TOKEN,
      onBeforeGenerateToken: async (pathname, _clientPayload) => {
        // Validate file types for employee allocation data
        const allowedContentTypes = [
          "text/csv",
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ];

        return {
          allowedContentTypes,
          addRandomSuffix: true,
          pathname: `${env.NODE_ENV}/${organizationId}/${pathname}`,
          tokenPayload: JSON.stringify({
            userId,
            organizationId,
            uploadType: "employee-allocation",
            timestamp: Date.now(),
          }),
        };
      },
      onUploadCompleted: async ({ blob, tokenPayload }) => {
        try {
          // Parse the token payload
          if (tokenPayload) {
            const payload = JSON.parse(tokenPayload);
            // Log for monitoring purposes (consider using a proper logging service)
            console.log("File upload completed:", {
              userId: payload.userId,
              organizationId: payload.organizationId,
              filename: blob.pathname,
              url: blob.url,
              environment: payload.environment,
            });
          }
        } catch (error) {
          console.error("Error processing upload completion:", error);
        }
      },
    });

    return NextResponse.json(jsonResponse);
  } catch (error) {
    console.error("Error handling blob upload:", error);
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 400 }
    );
  }
}
