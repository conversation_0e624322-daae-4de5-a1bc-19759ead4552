import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { env } from "~/env";

export async function GET(): Promise<NextResponse> {
  try {
    // Authenticate the user
    const { userId, orgId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get organization ID (fallback to user ID if no org)
    const organizationId = orgId || userId;

    // Determine environment
    const environment = env.NODE_ENV === "production" ? "prod" : "dev";

    return NextResponse.json({
      environment,
      organizationId,
    });
  } catch (error) {
    console.error("Error getting upload path:", error);
    return NextResponse.json(
      { error: "Failed to get upload path" },
      { status: 500 }
    );
  }
}
