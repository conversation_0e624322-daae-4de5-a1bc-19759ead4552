import { nanoid } from "nanoid";
import { NextRequest, NextResponse } from "next/server";
import { env } from "~/env";
import {
  abortMultipartUpload,
  completeMultipartUpload,
  createMultipartUpload,
} from "~/lib/s3";

export async function POST(request: NextRequest) {
  try {
    const { fileName, contentType, partCount, action, uploadId, parts } =
      await request.json();

    // For initialization of multipart upload
    if (action === "init") {
      if (!fileName || !contentType || !partCount) {
        return NextResponse.json(
          { error: "fileName, contentType, and partCount are required" },
          { status: 400 }
        );
      }

      // Generate a unique key for the file using the same structure as presigned-upload
      const prefix =
        env.NODE_ENV === "development" ? "dev-data" : "partner-data";
      const key = `${prefix}/${nanoid()}-${fileName}`;

      // Initialize the multipart upload
      const multipartData = await createMultipartUpload(
        key,
        contentType,
        partCount
      );

      // Generate the final URL that the file will have - using standard endpoint
      const url = `https://shika-us-west-1.s3.us-west-1.amazonaws.com/${multipartData.key}`;

      return NextResponse.json({
        ...multipartData,
        url,
      });
    }

    // For completing a multipart upload
    if (action === "complete") {
      if (!uploadId || !parts || !fileName) {
        return NextResponse.json(
          { error: "uploadId, parts, and fileName are required" },
          { status: 400 }
        );
      }

      // Validate that parts are in ascending order
      for (let i = 1; i < parts.length; i++) {
        if (parts[i].PartNumber <= parts[i - 1].PartNumber) {
          console.error("Parts not in ascending order:", JSON.stringify(parts));
          return NextResponse.json(
            { error: "Parts must be in ascending order by PartNumber" },
            { status: 400 }
          );
        }
      }

      // The key would be from the initial request - handling both old and new prefix formats
      let key = fileName;

      // If it doesn't already have a prefix structure, add the appropriate one
      if (!fileName.includes("/")) {
        const prefix =
          env.NODE_ENV === "development" ? "dev-data" : "partner-data";
        key = `${prefix}/${fileName}`;
      }

      // Ensure parts are sorted by part number (double-check)
      const sortedParts = [...parts].sort(
        (a, b) => a.PartNumber - b.PartNumber
      );

      await completeMultipartUpload(key, uploadId, sortedParts);

      // Use standard endpoint for the final URL
      const url = `https://shika-us-west-1.s3.us-west-1.amazonaws.com/${key}`;

      return NextResponse.json({
        success: true,
        url,
      });
    }

    // For aborting a multipart upload
    if (action === "abort") {
      if (!uploadId || !fileName) {
        return NextResponse.json(
          { error: "uploadId and fileName are required" },
          { status: 400 }
        );
      }

      // Handle both old and new prefix formats
      let key = fileName;

      // If it doesn't already have a prefix structure, add the appropriate one
      if (!fileName.includes("/")) {
        const prefix =
          env.NODE_ENV === "development" ? "dev-data" : "partner-data";
        key = `${prefix}/${fileName}`;
      }

      await abortMultipartUpload(key, uploadId);

      return NextResponse.json({
        success: true,
      });
    }

    return NextResponse.json(
      { error: "Invalid action specified" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error handling multipart upload:", error);
    return NextResponse.json(
      { error: "Failed to process multipart upload request" },
      { status: 500 }
    );
  }
}
