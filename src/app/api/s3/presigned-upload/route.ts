import { nanoid } from "nanoid";
import { NextRequest, NextResponse } from "next/server";
import { env } from "~/env";
import { getPresignedUploadUrl } from "~/lib/s3";

export async function POST(request: NextRequest) {
  console.log("Generating presigned URL", request);
  try {
    const { fileName, contentType } = await request.json();

    if (!fileName || !contentType) {
      return NextResponse.json(
        { error: "fileName and contentType are required" },
        { status: 400 }
      );
    }

    // Generate a unique key for the file
    // Format: uploads/{random-id}-{filename}
    const prefix = env.NODE_ENV === "development" ? "dev-data" : "partner-data";
    const key = `${prefix}/${nanoid()}-${fileName}`;

    // Get the presigned URL for uploading
    const presignedUrl = await getPresignedUploadUrl(key, contentType);

    // Generate a URL for accessing the file after upload using standard endpoint
    const url = `https://project-shika-dev.s3.us-east-2.amazonaws.com/${key}`;

    return NextResponse.json({
      presignedUrl,
      key,
      url,
    });
  } catch (error) {
    console.error("Error generating presigned URL:", error);
    return NextResponse.json(
      { error: "Failed to generate presigned URL" },
      { status: 500 }
    );
  }
}
