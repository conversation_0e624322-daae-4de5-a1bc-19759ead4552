export const INDUSTRIES = [
  { value: "AI", label: "AI" },
  { value: "<PERSON>WA", label: "<PERSON><PERSON>" },
  { value: "L1", label: "L1" },
  { value: "L2", label: "L2" },
  { value: "DePIN", label: "DePIN" },
  { value: "Gaming", label: "Gaming" },
  { value: "Marketplace", label: "Marketplace" },
  { value: "Oracle", label: "Oracle" },
  { value: "Other", label: "Other" },
] as const;

export const TEAM_SIZES = [
  { value: "1-50", label: "1-50" },
  { value: "51-100", label: "51-100" },
  { value: "101-150", label: "101-150" },
  { value: "151-200", label: "151-200" },
  { value: "201-250", label: "201-250" },
  { value: "251-300", label: "251-300" },
  { value: "300+", label: "300+" },
] as const;

export const INVESTOR_SIZES = [
  { value: "1-10", label: "1-10" },
  { value: "11-20", label: "11-20" },
  { value: "21-30", label: "21-30" },
  { value: "31-40", label: "31-40" },
  { value: "41-50", label: "41-50" },
  { value: "50+", label: "50+" },
] as const;

export type Industry = (typeof INDUSTRIES)[number]["value"];
export type TeamSize = (typeof TEAM_SIZES)[number]["value"];
export type InvestorSize = (typeof INVESTOR_SIZES)[number]["value"];

export const USER_TYPES = [
  "Founder",
  "C-Suite",
  "Finance",
  "Ecosystem",
  "Legal",
] as const;

export const STAKEHOLDER_TYPES = [
  "Advisor",
  "Investor",
  "Employee",
  "Founder",
  "Consultant",
  "Ex-Employee",
] as const;

export const GROUP_TYPES = [
  "Series A",
  "Series B",
  "Team",
  "Common",
  "Founders",
  "Advisor",
  "Advisors",
  "Labs Treasury",
  "Pre-TGE Consultants",
  "Options Available",
  "BOD",
  "Board Member",
] as const;

export const VESTING_FREQUENCIES = [
  "DAILY",
  "WEEKLY",
  "MONTHLY",
  "QUARTERLY",
  "ANNUALLY",
  "ONE_TIME",
] as const;

export const VESTING_TYPES = ["LINEAR", "NONLINEAR"] as const;

export const TOKEN_DISTRIBUTION_SUMMARY_CATEGORIES = [
  "Insider Pool",
  "Public Sale",
  "Private Sale",
  "Token Warrant Holders",
  "Foundation Treasury",
  "Ecosystem & Community",
  "Team",
  "Airdrop",
  "Other",
] as const;
