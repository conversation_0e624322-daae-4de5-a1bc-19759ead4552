import {
  Abort<PERSON>ultipartUploadCommand,
  CompleteMultipartUploadCommand,
  CreateMultipartUploadCommand,
  DeleteObjectCommand,
  GetObjectCommand,
  ListObjectsV2Command,
  PutObjectCommand,
  S3Client,
  UploadPartCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { env } from "../env";

const BUCKET = "shika-us-west-1";
// Initialize the S3 client with standard configuration (no acceleration)
export const s3Client = new S3Client({
  region: "us-west-1",
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
});

/**
 * Upload a file to S3
 * @param key - The key (path) where the file will be stored in the bucket
 * @param body - The file content
 * @param contentType - The content type of the file
 * @returns Promise with the result of the upload
 */
export async function uploadFile(
  key: string,
  body: Buffer | Uint8Array | string,
  contentType: string
) {
  const command = new PutObjectCommand({
    Bucket: BUCKET,
    Key: key,
    Body: body,
    ContentType: contentType,
  });

  return s3Client.send(command);
}

/**
 * Generate a pre-signed URL for uploading a file directly to S3
 * @param key - The key (path) where the file will be stored in the bucket
 * @param contentType - The content type of the file
 * @param expiresIn - How long the URL is valid for (in seconds)
 * @returns Promise with the pre-signed URL
 */
export async function getPresignedUploadUrl(
  key: string,
  contentType: string,
  expiresIn = 3600
) {
  const command = new PutObjectCommand({
    Bucket: BUCKET,
    Key: key,
    ContentType: contentType,
  });

  return getSignedUrl(s3Client, command, { expiresIn });
}

/**
 * Generate a pre-signed URL for downloading a file from S3
 * @param key - The key (path) of the file in the bucket
 * @param expiresIn - How long the URL is valid for (in seconds)
 * @returns Promise with the pre-signed URL
 */
export async function getPresignedDownloadUrl(key: string, expiresIn = 3600) {
  const command = new GetObjectCommand({
    Bucket: BUCKET,
    Key: key,
  });

  return getSignedUrl(s3Client, command, { expiresIn });
}

/**
 * Delete a file from S3
 * @param key - The key (path) of the file in the bucket
 * @returns Promise with the result of the deletion
 */
export async function deleteFile(key: string) {
  const command = new DeleteObjectCommand({
    Bucket: BUCKET,
    Key: key,
  });

  return s3Client.send(command);
}

/**
 * List files in an S3 bucket with an optional prefix
 * @param prefix - Optional prefix to filter results (folder path)
 * @returns Promise with the list of objects
 */
export async function listFiles(prefix?: string) {
  const command = new ListObjectsV2Command({
    Bucket: BUCKET,
    Prefix: prefix,
  });

  return s3Client.send(command);
}

/**
 * Get a file from S3
 * @param key - The key (path) of the file in the bucket
 * @returns Promise with the file data
 */
export async function getFile(key: string) {
  const command = new GetObjectCommand({
    Bucket: BUCKET,
    Key: key,
  });

  const response = await s3Client.send(command);

  // Convert the readable stream to a buffer
  if (response.Body) {
    const streamToBuffer = async (stream: ReadableStream): Promise<Buffer> => {
      const chunks: Uint8Array[] = [];
      const reader = stream.getReader();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }

      return Buffer.concat(chunks);
    };

    const buffer = await streamToBuffer(
      response.Body as unknown as ReadableStream
    );
    return { buffer, contentType: response.ContentType };
  }

  throw new Error("Failed to get file from S3");
}

/**
 * Initialize a multipart upload and return presigned URLs for each part
 * @param key - The key (path) where the file will be stored in the bucket
 * @param contentType - The content type of the file
 * @param partCount - The number of parts to create
 * @param expiresIn - How long the URLs are valid for (in seconds)
 * @returns Promise with the upload ID and presigned URLs for each part
 */
export async function createMultipartUpload(
  key: string,
  contentType: string,
  partCount: number,
  expiresIn = 3600
) {
  // Step 1: Create a multipart upload
  const createCommand = new CreateMultipartUploadCommand({
    Bucket: BUCKET,
    Key: key,
    ContentType: contentType,
  });

  const { UploadId } = await s3Client.send(createCommand);

  if (!UploadId) {
    throw new Error("Failed to create multipart upload");
  }

  // Step 2: Generate presigned URLs for each part
  const presignedUrls: string[] = [];

  for (let partNumber = 1; partNumber <= partCount; partNumber++) {
    const uploadPartCommand = new UploadPartCommand({
      Bucket: BUCKET,
      Key: key,
      UploadId,
      PartNumber: partNumber,
    });

    // S3 client is already configured with useAccelerateEndpoint: true
    const presignedUrl = await getSignedUrl(s3Client, uploadPartCommand, {
      expiresIn,
    });

    presignedUrls.push(presignedUrl);
  }

  return {
    uploadId: UploadId,
    presignedUrls,
    key,
  };
}

/**
 * Complete a multipart upload
 * @param key - The key (path) of the file in the bucket
 * @param uploadId - The upload ID returned from createMultipartUpload
 * @param parts - The ETags and part numbers of the uploaded parts
 * @returns Promise with the result of the completion
 */
export async function completeMultipartUpload(
  key: string,
  uploadId: string,
  parts: { ETag: string; PartNumber: number }[]
) {
  // Ensure parts are in ascending order by part number
  const sortedParts = [...parts].sort((a, b) => a.PartNumber - b.PartNumber);

  console.log(
    "S3 completeMultipartUpload with parts:",
    JSON.stringify(sortedParts)
  );

  const command = new CompleteMultipartUploadCommand({
    Bucket: BUCKET,
    Key: key,
    UploadId: uploadId,
    MultipartUpload: {
      Parts: sortedParts,
    },
  });

  return s3Client.send(command);
}

/**
 * Abort a multipart upload
 * @param key - The key (path) of the file in the bucket
 * @param uploadId - The upload ID returned from createMultipartUpload
 * @returns Promise with the result of the abort operation
 */
export async function abortMultipartUpload(key: string, uploadId: string) {
  const command = new AbortMultipartUploadCommand({
    Bucket: BUCKET,
    Key: key,
    UploadId: uploadId,
  });

  return s3Client.send(command);
}
