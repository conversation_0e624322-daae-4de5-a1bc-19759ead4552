import { render } from "@react-email/render";
import { Resend } from "resend";
import { MagicLinkEmail } from "~/components/emails/magic-link-email";
import { OrganizationInvitationEmail } from "~/components/emails/organization-invitation-email";
import { env } from "~/env";
import { APP_NAME, getBaseUrl } from "~/lib/utils";

export const NO_REPLY_EMAIL = "<EMAIL>";
export const resend = new Resend(env.RESEND_API_KEY);

export const renderMagicLinkEmail = async (url: string): Promise<string> => {
  return await render(MagicLinkEmail({ url }));
};

export const renderOrganizationInvitationEmail = async (
  url: string,
  organizationName: string,
  inviterName: string
): Promise<string> => {
  return await render(
    OrganizationInvitationEmail({ url, organizationName, inviterName })
  );
};

interface SendInvitationEmailParams {
  invitationId: string;
  email: string;
  organizationName: string;
  inviterName: string;
}

export async function sendInvitationEmail({
  invitationId,
  email,
  organizationName,
  inviterName,
}: SendInvitationEmailParams) {
  const inviteLink = `${getBaseUrl()}/accept-invitation/${invitationId}`;

  if (env.NODE_ENV === "development") {
    console.log("sendInvitationEmail", {
      email,
      organizationName,
      inviterName,
      inviteLink,
    });
    return;
  } else {
    console.log("sending invitation email", {
      organizationName,
      email,
    });
  }

  const html = await renderOrganizationInvitationEmail(
    inviteLink,
    organizationName,
    inviterName
  );

  await resend.emails.send({
    from: NO_REPLY_EMAIL,
    to: email,
    subject: `You've been invited to join ${organizationName} on ${APP_NAME}`,
    html,
  });
}

interface SendMagicLinkEmailParams {
  email: string;
  url: string;
  isExistingUser: boolean;
}

export async function sendMagicLinkEmail({
  email,
  url,
  isExistingUser,
}: SendMagicLinkEmailParams) {
  if (env.NODE_ENV === "development") {
    console.log("sendMagicLinkEmail", {
      email,
      url,
      isExistingUser,
    });
    return;
  }

  const html = await renderMagicLinkEmail(url);

  await resend.emails.send({
    from: NO_REPLY_EMAIL,
    to: email,
    subject: isExistingUser ? "Your magic link" : `Welcome to ${APP_NAME}`,
    html,
  });
}
