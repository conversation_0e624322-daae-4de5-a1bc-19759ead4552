/**
 * Utility functions for converting between decimal and percentage values
 * for allocation data after removing redundant percentage fields from the database.
 */

/**
 * Convert a decimal value (0-1) to percentage (0-100)
 * @param decimal - The decimal value (e.g., 0.155)
 * @returns The percentage value (e.g., 15.5)
 */
export function decimalToPercentage(
  decimal: number | string | null | undefined
): number {
  if (decimal === null || decimal === undefined) return 0;
  const numericValue =
    typeof decimal === "string" ? Number.parseFloat(decimal) : decimal;
  if (Number.isNaN(numericValue)) return 0;
  return numericValue * 100;
}

/**
 * Convert a percentage value (0-100) to decimal (0-1)
 * @param percentage - The percentage value (e.g., 15.5)
 * @returns The decimal value (e.g., 0.155)
 */
export function percentageToDecimal(
  percentage: number | string | null | undefined
): number {
  if (percentage === null || percentage === undefined) return 0;
  const numericValue =
    typeof percentage === "string" ? Number.parseFloat(percentage) : percentage;
  if (Number.isNaN(numericValue)) return 0;
  return numericValue / 100;
}

/**
 * Format a decimal value as a percentage string
 * @param decimal - The decimal value (e.g., 0.155)
 * @param decimalPlaces - Number of decimal places to show (default: 1)
 * @returns Formatted percentage string (e.g., "15.5%")
 */
export function formatDecimalAsPercentage(
  decimal: number | string | null | undefined,
  decimalPlaces: number = 1
): string {
  const percentage = decimalToPercentage(decimal);
  return `${percentage.toFixed(decimalPlaces)}%`;
}

/**
 * Safely get a numeric value from a Prisma Decimal field
 * @param decimal - The Prisma Decimal value
 * @returns The numeric value or 0 if invalid
 */
export function getNumericValue(decimal: unknown): number {
  if (decimal === null || decimal === undefined) return 0;
  const numericValue =
    typeof decimal === "string" ? Number.parseFloat(decimal) : Number(decimal);
  return Number.isNaN(numericValue) ? 0 : numericValue;
}
