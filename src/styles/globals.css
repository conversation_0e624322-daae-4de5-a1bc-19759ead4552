@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));

@plugin "tailwindcss-animate";

/* Light Theme Definitions */
:root {
  /* Core Backgrounds & Text */
  --background: oklch(
    1 0 0
  ); /* Pure white main background as seen in light mode screenshot */
  --foreground: oklch(0.15 0.003 286); /* Dark text for light mode */

  /* Tooltip */
  --tooltip-background: oklch(0.15 0.003 286); /* Black in light mode */
  --tooltip-foreground: oklch(1 0 0); /* White text on black background */

  /* Sidebar */
  --sidebar: oklch(
    0.96 0.003 286
  ); /* Very light gray sidebar background in light mode */
  --sidebar-foreground: oklch(0.25 0.01 286); /* Sidebar text color */
  --sidebar-border: oklch(0.9 0.003 286); /* Subtle sidebar border */
  --sidebar-hover: oklch(0.92 0.003 286); /* Hover state for sidebar items */
  --sidebar-active: oklch(0.9 0.003 286); /* Active item background */
  --sidebar-active-foreground: oklch(
    0.55 0.28 285
  ); /* Purple text for active items */
  --sidebar-accent: var(
    --sidebar-active
  ); /* Map accent to active for consistency */
  --sidebar-accent-foreground: var(
    --sidebar-active-foreground
  ); /* Map accent foreground to active foreground */

  /* Containers */
  --card: oklch(0.98 0.003 286);
  --card-foreground: var(--foreground);
  --popover: oklch(1 0 0);
  --popover-foreground: var(--foreground);

  /* Brand Colors */
  --primary: oklch(
    0.55 0.28 285
  ); /* Purple accent color matched from screenshots */
  --primary-foreground: oklch(1 0 0); /* White text on primary */

  /* Neutral UI Elements */
  --secondary: oklch(0.95 0.005 286); /* Light gray for secondary elements */
  --secondary-foreground: oklch(0.3 0.005 286);
  --muted: oklch(0.93 0.005 286); /* Muted background */
  --muted-foreground: oklch(0.5 0.005 286); /* Muted text */
  --accent: oklch(0.93 0.01 286); /* Light accent background */
  --accent-foreground: oklch(0.25 0.01 286);

  /* Filter Pill Colors */
  --filter-pill: oklch(0.93 0.005 286); /* Background for filter pills */
  --filter-pill-foreground: oklch(0.2 0.005 286); /* Text for filter pills */

  /* Status Colors */
  --success: oklch(0.55 0.15 150); /* Success green for paid status */
  --success-foreground: oklch(1 0 0);
  --destructive: oklch(0.65 0.25 25); /* Red */
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --warning: oklch(0.7 0.2 90); /* Amber/yellow */
  --warning-foreground: oklch(0.1 0.01 90);

  /* Borders & Rings */
  --border: oklch(0.9 0.003 286); /* Light border color */
  --input: oklch(0.9 0.003 286); /* Input border color */
  --ring: oklch(0.55 0.28 285 / 0.3); /* Purple focus ring */

  /* Charts */
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(340 75% 55%);
  --chart-3: hsl(31 91% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(160 60% 45%);

  /* Radius */
  --radius: 0.5rem;
}

/* Dark Theme Definitions */
.dark {
  /* Core Backgrounds & Text */
  --background: oklch(
    0.18 0.01 280
  ); /* Very dark, slightly purplish-blue base background */
  --foreground: oklch(0.95 0.005 280); /* Off-white text for better contrast */

  /* Tooltip */
  --tooltip-background: oklch(1 0 0); /* White in dark mode */
  --tooltip-foreground: oklch(
    0.15 0.003 286
  ); /* Black text on white background */

  /* Sidebar */
  --sidebar: oklch(0.22 0.01 280); /* Slightly lighter than main background */
  --sidebar-foreground: oklch(0.88 0.005 280); /* Lighter gray sidebar text */
  --sidebar-border: oklch(
    0.25 0.01 280
  ); /* Subtle border slightly lighter than sidebar */
  --sidebar-hover: oklch(
    0.26 0.01 280
  ); /* Hover state slightly lighter than sidebar */
  --sidebar-active: oklch(
    0.26 0.01 280
  ); /* Active item background, same as hover */
  --sidebar-active-foreground: oklch(
    0.55 0.28 285
  ); /* Keep the primary purple for active text */
  --sidebar-accent: var(
    --sidebar-active
  ); /* Map accent to active for consistency */
  --sidebar-accent-foreground: var(
    --sidebar-active-foreground
  ); /* Map accent foreground to active foreground */

  /* Containers */
  /* Card uses main background in the screenshot */
  --card: oklch(
    0.22 0.01 280
  ); /* Match card/table background to main background */
  --card-foreground: var(--foreground);
  --popover: oklch(0.28 0.015 280); /* Distinctly lighter popover background */
  --popover-foreground: var(--foreground);

  /* Brand Colors */
  --primary: oklch(0.55 0.28 285); /* Keep the existing vibrant purple */
  --primary-foreground: oklch(1 0 0); /* Pure white text on primary */

  /* Neutral UI Elements */
  /* Used for inputs, filter pills */
  --secondary: oklch(0.24 0.01 280); /* Background for inputs, pills */
  --secondary-foreground: oklch(0.9 0.005 280); /* Text on secondary elements */
  --muted: oklch(
    0.24 0.01 280
  ); /* Similar to secondary for muted backgrounds */
  --muted-foreground: oklch(
    0.65 0.01 280
  ); /* Dimmer text (e.g., table headers) */
  --accent: oklch(0.24 0.01 280); /* Accent background, same as secondary */
  --accent-foreground: oklch(0.9 0.005 280); /* Text for accent */

  /* Filter Pill Colors (using secondary) */
  --filter-pill: var(
    --secondary
  ); /* Use secondary color for filter pill background */
  --filter-pill-foreground: var(
    --secondary-foreground
  ); /* Use secondary text color for filter pills */

  /* Status Colors */
  --success: oklch(0.55 0.15 150); /* Keep existing green */
  --success-foreground: oklch(1 0 0);
  --destructive: oklch(0.67 0.18 30); /* Softer red */
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --warning: oklch(0.7 0.2 90); /* Keep existing yellow */
  --warning-foreground: oklch(0.1 0.01 90);

  /* Borders & Rings */
  --border: oklch(
    0.26 0.01 280
  ); /* Subtle border slightly lighter than background/card */
  --input: var(--border); /* Input border same as general border */
  --ring: oklch(
    0.55 0.28 285 / 0.4
  ); /* Primary color focus ring with transparency */

  /* Charts (Keep existing or adjust as needed) */
  --chart-1: hsl(220 70% 50%);
  --chart-5: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-2: hsl(340 75% 55%);
}

/* Tailwind Theme Mapping */
@theme inline {
  /* Mapping core colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-filter-pill: var(--filter-pill);
  --color-filter-pill-foreground: var(--filter-pill-foreground);

  /* Mapping chart colors */
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  /* Mapping sidebar colors */
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-hover: var(--sidebar-hover);
  --color-sidebar-active: var(--sidebar-active);
  --color-sidebar-active-foreground: var(--sidebar-active-foreground);

  /* Misc */
  --color-transparent: transparent;

  /* Radius values */
  --radius-xs: calc(var(--radius) - 0.25rem);
  --radius-sm: calc(var(--radius) - 0.25rem);
  --radius-md: calc(var(--radius) - 0.125rem);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 0.25rem);

  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1400px;
}

/* Base Layer Styles */
@layer base {
  /* Tailwind v4 Border Color Compatibility */
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-border, currentColor);
  }

  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }

  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@theme inline {
  --animate-aurora: aurora 60s linear infinite;
  @keyframes aurora {
    from {
      background-position:
        50% 50%,
        50% 50%;
    }
    to {
      background-position:
        350% 50%,
        350% 50%;
    }
  }
}
