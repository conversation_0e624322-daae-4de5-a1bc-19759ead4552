"use client";

import {
  ColumnDef,
  getCoreRowModel,
  getFilteredRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { CheckIcon, CopyIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import { DataTable } from "~/components/tables/data-table";
import { Button } from "~/components/ui/button";
import { Skeleton } from "~/components/ui/skeleton";
import { api } from "~/trpc/react";

// Define the organization type based on what we actually use from Clerk's API response
type OrganizationData = {
  id: string;
  name: string;
  slug: string | null;
  createdAt: number;
  imageUrl?: string;
  hasImage?: boolean;
  updatedAt?: number;
  publicMetadata?: Record<string, unknown> | null;
  privateMetadata?: Record<string, unknown>;
  maxAllowedMemberships?: number;
  adminDeleteEnabled?: boolean;
  createdBy?: string;
};

// Copy button component
function CopyButton({ text, label }: { text: string; label?: string }) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      toast.success(`${label || "Text"} copied to clipboard!`);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error(`Failed to copy ${label || "text"}`);
      console.error("Error copying to clipboard:", error);
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={copyToClipboard}
      className="ml-1 h-6 px-2"
    >
      {copied ? (
        <CheckIcon className="h-3 w-3" />
      ) : (
        <CopyIcon className="h-3 w-3" />
      )}
    </Button>
  );
}

// Organizations table skeleton
function OrganizationsTableSkeleton() {
  const columns = Array.from({ length: 4 }, (_, i) => i);

  return (
    <div className="rounded-md">
      {/* Header skeleton */}
      <div className="bg-muted/50 h-11 rounded-md px-4">
        <div className="flex h-full items-center gap-4">
          {columns.map((_, index) => (
            <Skeleton key={index} className="h-4 w-full" />
          ))}
        </div>
      </div>

      {/* Rows skeleton */}
      <div>
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="px-4 py-3">
            <div className="flex items-center gap-4">
              {columns.map((_, j) => (
                <Skeleton key={j} className="h-4 w-full" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

interface OrganizationsTableProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
}

export function OrganizationsTable({
  searchQuery,
  onSearchChange,
}: OrganizationsTableProps) {
  const { data, isLoading, error } = api.admin.getOrganizations.useQuery();

  const columns: ColumnDef<OrganizationData>[] = [
    {
      accessorKey: "id",
      header: "ID",
      enableGlobalFilter: false,
      cell: ({ row }) => (
        <div className="flex w-full items-center">
          <div className="font-mono text-xs">{row.getValue("id")}</div>
          <CopyButton text={row.getValue("id")} label="Organization ID" />
        </div>
      ),
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => {
        const name = row.getValue("name") as string;
        return <div className="w-full font-medium">{name}</div>;
      },
    },
    {
      accessorKey: "slug",
      header: "Slug",
      cell: ({ row }) => {
        const slug = row.getValue("slug") as string | null;
        return (
          <div className="flex w-full items-center">
            <div className="font-mono text-xs">{slug || "—"}</div>
            {slug && <CopyButton text={slug} label="Slug" />}
          </div>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      enableGlobalFilter: false,
      cell: ({ row }) => {
        const createdAt = row.getValue("createdAt") as number;
        return (
          <div className="w-full">
            {new Date(createdAt).toLocaleDateString()}
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: data?.data ?? [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onGlobalFilterChange: onSearchChange,
    state: {
      globalFilter: searchQuery,
    },
  });

  if (isLoading) {
    return <OrganizationsTableSkeleton />;
  }

  if (error) {
    return (
      <div className="text-destructive">
        Error loading organizations: {error.message}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <DataTable
        columns={columns}
        data={data?.data ?? []}
        hideToolbar={true}
        table={table}
        className="overflow-auto"
      />

      <div className="flex items-center justify-between">
        {!searchQuery && (
          <div className="text-muted-foreground pl-4 text-sm">
            {data?.data.length || 0} organizations
          </div>
        )}
      </div>
    </div>
  );
}
