"use client";

import { useSignIn } from "@clerk/nextjs";
import {
  ColumnDef,
  getCoreRowModel,
  getFilteredRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { CheckIcon, CopyIcon, MoreHorizontalIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import { DataTable } from "~/components/tables/data-table";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Skeleton } from "~/components/ui/skeleton";
import { api } from "~/trpc/react";

type ClerkUser = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  emailAddresses: Array<{ emailAddress: string }>;
  createdAt: number;
};

// Copy button component
function CopyButton({ text, label }: { text: string; label?: string }) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      toast.success(`${label || "Text"} copied to clipboard!`);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error(`Failed to copy ${label || "text"}`);
      console.error("Error copying to clipboard:", error);
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={copyToClipboard}
      className="ml-1 h-6 px-2"
    >
      {copied ? (
        <CheckIcon className="h-3 w-3" />
      ) : (
        <CopyIcon className="h-3 w-3" />
      )}
    </Button>
  );
}

// Users table skeleton
function UsersTableSkeleton() {
  const columns = Array.from({ length: 5 }, (_, i) => i);

  return (
    <div className="rounded-md">
      {/* Header skeleton */}
      <div className="bg-muted/50 h-11 rounded-md px-4">
        <div className="flex h-full items-center gap-4">
          {columns.map((_, index) => (
            <Skeleton key={index} className="h-4 w-full" />
          ))}
        </div>
      </div>

      {/* Rows skeleton */}
      <div>
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="px-4 py-3">
            <div className="flex items-center gap-4">
              {columns.map((_, j) => (
                <Skeleton key={j} className="h-4 w-full" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

interface UsersTableProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
}

export function UsersTable({ searchQuery, onSearchChange }: UsersTableProps) {
  const { data, isLoading, error } = api.admin.getUsers.useQuery();
  const { isLoaded, signIn, setActive } = useSignIn();
  const router = useRouter();

  const { mutateAsync: generateImpersonationToken } =
    api.admin.generateImpersonationToken.useMutation({
      onSuccess: async (token) => {
        if (!isLoaded || !signIn) {
          toast.error("Authentication not ready");
          return;
        }

        try {
          if (!token.token) {
            throw new Error("No token found");
          }

          const { createdSessionId } = await signIn.create({
            strategy: "ticket",
            ticket: token.token,
          });

          await setActive({
            session: createdSessionId,
          });
          router.push("/");
          toast.success("Successfully impersonating user!");
        } catch (error) {
          console.error("Error during impersonation:", error);
          toast.error("Failed to impersonate user");
        }
      },
      onError: (error) => {
        toast.error(`Failed to generate impersonation token: ${error.message}`);
      },
    });

  const handleImpersonate = async (userId: string) => {
    try {
      await generateImpersonationToken({ userId });
    } catch (error) {
      console.error("Error impersonating user:", error);
    }
  };

  const columns: ColumnDef<ClerkUser>[] = [
    {
      accessorKey: "id",
      header: "ID",
      enableGlobalFilter: false,
      cell: ({ row }) => (
        <div className="flex w-full items-center">
          <div className="font-mono text-xs">{row.getValue("id")}</div>
          <CopyButton text={row.getValue("id")} label="User ID" />
        </div>
      ),
    },
    {
      accessorKey: "firstName",
      header: "First Name",
      cell: ({ row }) => {
        const firstName = row.getValue("firstName") as string | null;
        return <div className="w-full">{firstName || "—"}</div>;
      },
    },
    {
      accessorKey: "lastName",
      header: "Last Name",
      cell: ({ row }) => {
        const lastName = row.getValue("lastName") as string | null;
        return <div className="w-full">{lastName || "—"}</div>;
      },
    },
    {
      accessorKey: "emailAddresses",
      header: "Email",
      cell: ({ row }) => {
        const emailAddresses = row.getValue("emailAddresses") as Array<{
          emailAddress: string;
        }>;
        const primaryEmail = emailAddresses[0]?.emailAddress || "No email";
        return (
          <div className="flex w-full items-center">
            <div className="font-mono text-xs">{primaryEmail}</div>
            {primaryEmail !== "No email" && (
              <CopyButton text={primaryEmail} label="Email" />
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      enableGlobalFilter: false,
      cell: ({ row }) => {
        const createdAt = row.getValue("createdAt") as number;
        return (
          <div className="w-full">
            {new Date(createdAt).toLocaleDateString()}
          </div>
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <div className="flex justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontalIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => handleImpersonate(row.getValue("id"))}
                >
                  Impersonate
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: data?.data ?? [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onGlobalFilterChange: onSearchChange,
    state: {
      globalFilter: searchQuery,
    },
  });

  if (isLoading) {
    return <UsersTableSkeleton />;
  }

  if (error) {
    return (
      <div className="text-destructive">
        Error loading users: {error.message}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <DataTable
        columns={columns}
        data={data?.data ?? []}
        hideToolbar={true}
        table={table}
        className="overflow-auto"
      />

      <div className="flex items-center justify-between">
        {!searchQuery && (
          <div className="text-muted-foreground pl-4 text-sm">
            {data?.data.length || 0} users
          </div>
        )}
      </div>
    </div>
  );
}
