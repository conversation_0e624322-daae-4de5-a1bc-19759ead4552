"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { PlusIcon } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { <PERSON><PERSON> } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { SubmitButton } from "~/components/ui/submit-button";
import { api } from "~/trpc/react";

const createOrgSchema = z.object({
  organizationName: z.string().min(1, "Organization name is required"),
  adminEmail: z.string().email("Valid email is required"),
});

type CreateOrgFormValues = z.infer<typeof createOrgSchema>;

export function CreateOrganizationDialog() {
  const [open, setOpen] = useState(false);
  const utils = api.useUtils();

  const form = useForm<CreateOrgFormValues>({
    resolver: zodResolver(createOrgSchema),
    defaultValues: {
      organizationName: "",
      adminEmail: "",
    },
  });

  const { mutateAsync: createOrganizationWithAdmin, isPending } =
    api.admin.createOrganizationWithAdmin.useMutation({
      onSuccess: (data) => {
        toast.success(
          `Organization "${data.organization.name}" created successfully with admin user!`
        );
        form.reset();
        setOpen(false);
        void utils.admin.getOrganizations.invalidate();
      },
      onError: (error) => {
        toast.error(`Failed to create organization: ${error.message}`);
      },
    });

  const onSubmit = async (values: CreateOrgFormValues) => {
    try {
      await createOrganizationWithAdmin(values);
    } catch (error) {
      // Error is handled by the mutation's onError callback
      console.error("Error creating organization:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <PlusIcon className="h-4 w-4" />
          Create Organization
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Organization</DialogTitle>
          <DialogDescription>
            Create a new organization with an admin user. The admin user can
            sign in using magic links or OAuth to manage the organization.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="organizationName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organization Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Acme Corp" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="adminEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Admin Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    The admin user can sign in using magic links or OAuth
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isPending}
              >
                Cancel
              </Button>
              <SubmitButton
                type="submit"
                disabled={isPending}
                isSubmitting={isPending}
              >
                Create Organization
              </SubmitButton>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
