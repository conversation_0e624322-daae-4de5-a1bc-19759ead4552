"use client";
import {
  <PERSON>UpRightIcon,
  BarChart3Icon,
  PieChartIcon,
  UsersIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";
import { Badge } from "~/components/ui/badge";
import { cn } from "~/lib/utils";

export interface QuickLinkAction {
  title: string;
  description: string;
  icon: React.ReactNode;
  bgColor: string;
  borderColor: string;
  link?: string | null;
  disabled?: boolean;
  badge?: string;
}

export const actions: QuickLinkAction[] = [
  {
    title: "Benchmarks",
    description:
      "See token allocation benchmarks and average based on Insider background and/or company.",
    icon: <UsersIcon className="text-primary h-6 w-6" />,
    bgColor: "bg-card",
    borderColor: "border-border",
    link: "/benchmarks",
  },
  {
    title: "Insights",
    description:
      "Leverage valuable industry metrics and insights relevant to your benchmarks",
    icon: <PieChartIcon className="text-primary h-6 w-6" />,
    bgColor: "bg-card",
    borderColor: "border-border",
    link: "/insights",
  },
  {
    title: "Model",
    description:
      "Get personalized recommendations on benchmarks based on your company details.",
    icon: <BarChart3Icon className="text-primary h-6 w-6" />,
    bgColor: "bg-card",
    borderColor: "border-border",
    link: "/modeling",
    disabled: false,
  },
];

export function QuickLinks() {
  const router = useRouter();
  return (
    <div className="grid w-full grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {actions.map((action, index) => (
        <div
          key={index}
          className={cn(
            "group rounded-xl border p-6 transition-all duration-200 hover:scale-[1.01] hover:shadow-md",
            action.borderColor,
            action.bgColor,
            action.disabled ? "cursor-not-allowed opacity-50" : "cursor-pointer"
          )}
          onClick={() => {
            if (action.link && !action.disabled) {
              router.push(action.link);
            }
          }}
          tabIndex={action.link && !action.disabled ? 0 : -1}
          role={action.link && !action.disabled ? "button" : undefined}
          aria-disabled={action.disabled}
        >
          <div className="flex items-start justify-between">
            <div className="mb-4 flex flex-shrink-0 items-center gap-2">
              {action.icon}
              {action.badge && (
                <Badge size="small" className="ml-1">
                  {action.badge}
                </Badge>
              )}
            </div>
            {!action.disabled && (
              <ArrowUpRightIcon className="text-muted-foreground h-5 w-5 opacity-0 transition-opacity group-hover:opacity-100" />
            )}
          </div>
          <h3 className="mb-2 text-lg font-medium">{action.title}</h3>
          <p className="text-muted-foreground text-sm">{action.description}</p>
        </div>
      ))}
    </div>
  );
}
