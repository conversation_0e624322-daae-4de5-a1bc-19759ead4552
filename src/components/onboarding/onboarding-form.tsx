"use client";

import { useUser } from "@clerk/nextjs";
import { defineStepper } from "@stepperize/react";
import {
  CheckIcon,
  FileTextIcon,
  TrendingUpIcon,
  UserIcon,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { api } from "~/trpc/react";
import { EmployeeInfoStep } from "./employee-info-step";
import { InvestorInfoStep } from "./investor-info-step";
import { OnboardingComplete } from "./onboarding-complete";
import {
  PersonalInfoStep,
  type PersonalInfoFormValues,
} from "./personal-info-step";

// Define the stepper steps
const { useStepper, Scoped } = defineStepper(
  { id: "personal-info", title: "Personal Information" },
  { id: "investor-info", title: "Investor Information" },
  { id: "employee-info", title: "Employee Information" },
  { id: "complete", title: "Complete" }
);

interface OnboardingFormProps {
  user: {
    id: string;
    name: string | null;
  };
}

/**
 * Onboarding form component using Stepperize for step management
 */
export function OnboardingForm({ user }: OnboardingFormProps) {
  return (
    <Scoped>
      <OnboardingFormContent user={user} />
    </Scoped>
  );
}

function OnboardingFormContent({ user }: OnboardingFormProps) {
  const [_isSubmitting, setIsSubmitting] = useState(false);
  const [personalData, setPersonalData] =
    useState<PersonalInfoFormValues | null>(null);
  const { user: clerkUser } = useUser();

  // Use the stepper hook
  const stepper = useStepper();

  // Get icon for each step
  const getStepIcon = (stepId: string, isCompleted: boolean) => {
    const iconProps = { className: "h-4 w-4" };

    if (isCompleted) {
      return <CheckIcon {...iconProps} />;
    }

    switch (stepId) {
      case "personal-info": {
        return <UserIcon {...iconProps} />;
      }
      case "investor-info": {
        return <TrendingUpIcon {...iconProps} />;
      }
      case "employee-info": {
        return <FileTextIcon {...iconProps} />;
      }
      case "complete": {
        return <CheckIcon {...iconProps} />;
      }
      default: {
        return <UserIcon {...iconProps} />;
      }
    }
  };

  const { mutateAsync: completeOnboardingMutation } =
    api.onboarding.completeOnboarding.useMutation({
      onSuccess: (resp) => {
        if (resp.success) {
          clerkUser?.reload();
        } else {
          toast.error("Something went wrong. Please try again.");
        }
      },
      onError: (error) => {
        console.error("Error in form submission:", error);
        setIsSubmitting(false);
        toast.error("Something went wrong. Please try again.");
      },
    });

  // Handle personal info completion
  const handlePersonalInfoComplete = async (data: PersonalInfoFormValues) => {
    setPersonalData(data);
    stepper.next();
  };

  // Handle investor info completion
  const handleInvestorInfoComplete = async () => {
    stepper.next();
  };

  // Handle employee info completion and final onboarding
  const handleEmployeeInfoComplete = async () => {
    if (!personalData) {
      toast.error("Personal information is missing. Please start over.");
      stepper.goTo("personal-info");
      return;
    }

    setIsSubmitting(true);

    try {
      await completeOnboardingMutation({
        firstName: personalData.firstName,
        lastName: personalData.lastName,
        role: personalData.userType,
      });
      stepper.next(); // Move to complete step
    } catch (error) {
      console.error("Error completing onboarding:", error);
      toast.error("Failed to complete onboarding. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // If we're on the complete step, render it without the wrapper
  if (stepper.current.id === "complete") {
    return <OnboardingComplete />;
  }

  return (
    <div className="bg-background min-h-screen">
      <main className="flex min-h-[calc(100vh-theme(spacing.16))] flex-1 flex-col gap-4 p-4 sm:p-6 md:gap-8 md:p-8">
        <div className="mx-auto flex w-full max-w-2xl flex-grow items-center justify-center">
          <div className="flex w-full flex-col gap-6">
            {/* Step Progress Indicator */}
            <div className="flex items-center justify-center space-x-4">
              {stepper.all.map((step, index) => {
                const isActive = step.id === stepper.current.id;
                const isCompleted =
                  stepper.all.findIndex((s) => s.id === step.id) <
                  stepper.all.findIndex((s) => s.id === stepper.current.id);

                return (
                  <div key={step.id} className="flex items-center">
                    <div
                      className={`flex h-10 w-10 items-center justify-center rounded-full transition-colors ${
                        isCompleted
                          ? "bg-primary text-primary-foreground"
                          : isActive
                            ? "bg-primary text-primary-foreground"
                            : "bg-muted text-muted-foreground"
                      }`}
                      title={step.title}
                    >
                      {getStepIcon(step.id, isCompleted)}
                    </div>
                    {index < stepper.all.length - 1 && (
                      <div
                        className={`ml-4 h-0.5 w-8 transition-colors ${
                          isCompleted ? "bg-primary" : "bg-muted"
                        }`}
                      />
                    )}
                  </div>
                );
              })}
            </div>

            {/* Current Step Content */}
            {stepper.switch({
              "personal-info": () => (
                <PersonalInfoStep
                  user={user}
                  onComplete={handlePersonalInfoComplete}
                />
              ),
              "investor-info": () => (
                <InvestorInfoStep onComplete={handleInvestorInfoComplete} />
              ),
              "employee-info": () => (
                <EmployeeInfoStep onComplete={handleEmployeeInfoComplete} />
              ),
              complete: () => <OnboardingComplete />,
            })}
          </div>
        </div>
      </main>
    </div>
  );
}
