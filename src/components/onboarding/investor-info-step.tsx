"use client";

import { upload } from "@vercel/blob/client";
import {
  FileSpreadsheetIcon,
  FolderUpIcon,
  Loader2Icon,
  Trash2Icon,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { Button } from "~/components/ui/button";
import { Progress } from "~/components/ui/progress";
import { SubmitButton } from "~/components/ui/submit-button";

// File types for investor allocation data
const ACCEPTED_MIME_TYPES = {
  "text/csv": [".csv"],
  "application/vnd.ms-excel": [".xls"],
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
    ".xlsx",
  ],
};

interface InvestorInfoStepProps {
  onComplete: () => Promise<void>;
}

interface FileWithProgress {
  id: string;
  file: File;
  progress: number;
  status: "pending" | "uploading" | "completed" | "error";
  url?: string;
  error?: string;
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return (
    Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  );
}

export function InvestorInfoStep({ onComplete }: InvestorInfoStepProps) {
  const [files, setFiles] = useState<FileWithProgress[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const onCompleteStep = async () => {
    setIsSubmitting(true);
    await onComplete();
    setIsSubmitting(false);
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setFiles((prev) => [
      ...prev,
      ...acceptedFiles.map((file) => ({
        id: `${file.name}-${Date.now()}`,
        file,
        status: "pending" as const,
        progress: 0,
      })),
    ]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
    accept: ACCEPTED_MIME_TYPES,
    onDropRejected: (fileRejections) => {
      for (const rejection of fileRejections) {
        toast.error(`File "${rejection.file.name}" was rejected`, {
          description: "Only CSV, XLS, and XLSX files are supported.",
        });
      }
    },
  });

  const handleUpload = useCallback(
    async (fileId: string) => {
      const fileToUpload = files.find((f) => f.id === fileId);

      // Only upload if the file exists and is in 'pending' or 'error' state
      // and not already uploading.
      if (
        !fileToUpload ||
        fileToUpload.status === "uploading" ||
        fileToUpload.status === "completed"
      ) {
        return;
      }

      setFiles((prev) =>
        prev.map((f) =>
          f.id === fileId
            ? { ...f, status: "uploading", progress: 0, error: undefined }
            : f
        )
      );

      try {
        // Get environment and organization info from the server
        const pathResponse = await fetch("/api/blob/upload-path");
        const { environment, organizationId } = await pathResponse.json();

        // Construct the full path: env/orgId/filename
        const fullPath = `${environment}/${organizationId}/${fileToUpload.file.name}`;
        const result = await upload(fullPath, fileToUpload.file, {
          access: "public",
          handleUploadUrl: "/api/blob/upload",
          onUploadProgress: (progress) => {
            setFiles((prev) =>
              prev.map((f) =>
                f.id === fileId
                  ? { ...f, progress: Math.round(progress.percentage) }
                  : f
              )
            );
          },
          multipart: true,
        });

        setFiles((prev) =>
          prev.map((f) =>
            f.id === fileId
              ? { ...f, status: "completed", progress: 100, url: result.url }
              : f
          )
        );

        toast.success(`File "${fileToUpload.file.name}" uploaded successfully`);
      } catch (error) {
        console.error("Upload error:", error);
        setFiles((prev) =>
          prev.map((f) =>
            f.id === fileId
              ? {
                  ...f,
                  status: "error",
                  error:
                    error instanceof Error ? error.message : "Upload failed",
                }
              : f
          )
        );
        toast.error(`Failed to upload "${fileToUpload.file.name}"`);
      }
    },
    [files]
  );

  useEffect(() => {
    const pendingFiles = files.filter((f) => f.status === "pending");
    if (pendingFiles.length > 0) {
      for (const fileObj of pendingFiles) {
        if (fileObj.status === "pending") {
          handleUpload(fileObj.id);
        }
      }
    }
  }, [files, handleUpload]);

  const handleRemove = (fileId: string) => {
    setFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const getFileIcon = (_file: File) => {
    return <FileSpreadsheetIcon className="h-8 w-8 text-green-500" />;
  };

  const completedFiles = files.filter((f) => f.status === "completed");
  const hasFiles = files.length > 0;
  const canProceed = completedFiles.length > 0;

  return (
    <div className="flex w-full flex-col gap-6">
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">
          Upload Investor Captable
        </h1>
        <p className="text-muted-foreground mt-1.5 text-sm">
          When setting Token Allocations, the most common method of distributing
          is pro rata. For warrant holders, these values are set. For non token
          holders, these values are subject to the team. Upload your cap table
          information to get the best recommendations based on industry best
          practices.
        </p>
      </div>

      {/* File Upload Area */}
      <div
        {...getRootProps()}
        className={`cursor-pointer rounded-lg border-2 border-dashed transition-colors ${
          isDragActive
            ? "border-primary bg-primary/5"
            : "border-muted-foreground/25 hover:border-muted-foreground/50"
        }`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center px-4 py-12">
          <div className="bg-secondary mb-4 rounded-full p-3">
            <FolderUpIcon className="h-6 w-6" />
          </div>
          <p className="text-muted-foreground mb-4 text-sm">
            Supported formats: CSV, XLS, XLSX
          </p>
          <div className="flex gap-3">
            <Button variant="secondary" size="sm">
              Select Files
            </Button>
          </div>
        </div>
      </div>

      {/* File List */}
      {hasFiles && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Files ({files.length})</h3>
          </div>

          <div className="space-y-2">
            {files.map((fileObj) => (
              <div
                key={fileObj.id}
                className="flex items-center gap-3 rounded-lg border p-3"
              >
                {getFileIcon(fileObj.file)}
                <div className="min-w-0 flex-1">
                  <p className="truncate text-sm font-medium">
                    {fileObj.file.name}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    {formatFileSize(fileObj.file.size)}
                  </p>
                </div>

                <div className="ml-4 flex flex-col items-end gap-2">
                  <div className="flex items-center gap-2">
                    {fileObj.status === "uploading" && (
                      <Loader2Icon className="text-muted-foreground h-5 w-5 animate-spin" />
                    )}
                    {(fileObj.status === "completed" ||
                      fileObj.status === "error" ||
                      fileObj.status === "pending") && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent triggering file dialog
                          handleRemove(fileObj.id);
                        }}
                        disabled={fileObj.status === "pending"} // Disable remove if this specific file is uploading
                        className="text-destructive hover:text-destructive h-8 w-8"
                      >
                        <Trash2Icon className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  {/* Progress Bar and Status */}
                  <div className="flex w-full min-w-[100px] flex-col items-end justify-end gap-1">
                    {fileObj.status === "uploading" && (
                      <Progress
                        value={fileObj.progress}
                        className="h-1 w-full"
                      />
                    )}
                    {fileObj.status === "error" && (
                      <div className="flex flex-col items-end gap-1">
                        <Button
                          variant="link"
                          size="sm"
                          onClick={() => handleUpload(fileObj.id)}
                          className="text-primary hover:text-primary/80 h-auto p-0 text-xs"
                        >
                          Retry
                        </Button>
                        {fileObj.error && (
                          <p className="text-xs text-red-500">
                            {fileObj.error}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="mt-4 flex justify-end gap-3">
        <Button variant="outline" onClick={onCompleteStep}>
          Skip
        </Button>
        <SubmitButton
          onClick={onCompleteStep}
          disabled={
            !canProceed ||
            isSubmitting ||
            files.some(
              (f) => f.status === "uploading" || f.status === "pending"
            )
          }
          isSubmitting={isSubmitting}
        >
          Continue
        </SubmitButton>
      </div>
    </div>
  );
}
