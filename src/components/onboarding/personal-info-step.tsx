"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { ClipboardListIcon, UserIcon } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { Checkbox } from "~/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { SubmitButton } from "~/components/ui/submit-button";
import { USER_TYPES } from "~/lib/constants";

// Personal info form schema
const personalInfoSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  userType: z.enum(USER_TYPES),
  termsAccepted: z.boolean().refine((val) => val, {
    message: "Please read and accept the terms and conditions",
  }),
});

export type PersonalInfoFormValues = z.infer<typeof personalInfoSchema>;

interface PersonalInfoStepProps {
  user: {
    id: string;
    name: string | null;
  };
  onComplete: (data: PersonalInfoFormValues) => Promise<void>;
}

/**
 * Personal information step component for onboarding
 */
export function PersonalInfoStep({ user, onComplete }: PersonalInfoStepProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Personal info form
  const form = useForm<PersonalInfoFormValues>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      firstName: user.name?.split(" ")[0] || "",
      lastName: user.name?.split(" ").slice(1).join(" ") || "",
      userType: undefined,
      termsAccepted: false,
    },
  });

  // Submit personal info
  const submitPersonalInfo = async () => {
    setIsSubmitting(true);

    // Trigger validation for safety
    const isFormValid = await form.trigger();
    if (!isFormValid) {
      setIsSubmitting(false);
      toast.error("Please check your information.");
      return;
    }

    const formData = form.getValues();
    await onComplete(formData);
    setIsSubmitting(false);
  };

  // Check if the form is valid
  const isFormValid = form.formState.isValid;

  return (
    <div className="flex w-full flex-col gap-6">
      <div className="text-center">
        <h1 className="text-xl font-semibold tracking-tight">
          Tell us about yourself
        </h1>
        <p className="text-muted-foreground mt-1.5 text-sm">
          Let&apos;s start with your basic information
        </p>
      </div>

      <Form {...form}>
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <Label>
                    <div className="flex items-center gap-2">
                      <UserIcon className="text-muted-foreground h-4 w-4" />
                      First Name
                    </div>
                  </Label>
                  <FormControl>
                    <Input placeholder="Enter your first name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <Label>
                    <div className="flex items-center gap-2">Last Name</div>
                  </Label>
                  <FormControl>
                    <Input placeholder="Enter your last name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="userType"
            render={({ field }) => (
              <FormItem className="mt-4">
                <Label>
                  <div className="flex items-center gap-2">
                    <ClipboardListIcon className="text-muted-foreground h-4 w-4" />
                    Role
                  </div>
                </Label>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your role" />
                  </SelectTrigger>
                  <SelectContent>
                    {USER_TYPES.map((option) => (
                      <SelectItem key={option} value={option}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="termsAccepted"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <Label
                    htmlFor="termsAccepted"
                    className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    I agree to the{" "}
                    <a
                      href="/terms-of-service"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary underline"
                    >
                      Terms of Service
                    </a>
                  </Label>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          />
        </div>
      </Form>

      <SubmitButton
        type="button"
        onClick={submitPersonalInfo}
        className="w-full"
        disabled={isSubmitting || !isFormValid}
        isSubmitting={isSubmitting}
      >
        Continue
      </SubmitButton>
    </div>
  );
}
