import { motion } from "motion/react";
import { useRouter } from "next/navigation";
import { TypingAnimation } from "~/components/magicui/typing-animation";
import { AuroraBackground } from "~/components/ui/aurora-background";
import { But<PERSON> } from "~/components/ui/button";

export function OnboardingComplete() {
  const router = useRouter();
  return (
    <AuroraBackground>
      <motion.div
        initial={{ opacity: 0, y: 0 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{
          delay: 0.3,
          duration: 0.8,
          ease: "easeInOut",
        }}
        className="relative container flex max-w-[600px] flex-col justify-center gap-4 px-6"
      >
        <TypingAnimation
          delay={1}
          duration={150}
          className="text-foreground text-4xl tracking-tight"
        >
          Welcome to Blocksight
        </TypingAnimation>
        <p className="text-secondary-foreground">
          Access comprehensive tokenomics data across the Web3 ecosystem.
          Compare allocation benchmarks, vesting schedules, and airdrop
          information to make informed decisions.
        </p>
        <div>
          <Button onClick={() => router.replace("/")}>Get Started</Button>
        </div>
      </motion.div>
    </AuroraBackground>
  );
}
