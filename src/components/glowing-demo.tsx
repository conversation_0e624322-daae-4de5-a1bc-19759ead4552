"use client";

import { Badge } from "~/components/ui/badge";
import { GlowingEffect } from "~/components/ui/glowing-effect";

export function GlowingEffectDemo() {
  return (
    <ul className="flex gap-4 p-1">
      <GridItem
        title="Token Launch Process Overview"
        description="Learn detailed step-by-step on considerations and process to..."
      />

      <GridItem
        title="Token Launch Checklist"
        description="Track your progress to launch your token. See referral links for..."
      />
    </ul>
  );
}

interface GridItemProps {
  title: string;
  description: React.ReactNode;
}

const GridItem = ({ title, description }: GridItemProps) => {
  return (
    <li className="min-h-[14rem] w-[300px] cursor-pointer list-none transition-transform duration-200 hover:scale-[1.02]">
      <div className="relative h-full rounded-xl border p-2 hover:shadow-md md:p-3">
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
        />
        <div className="border-0.75 relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl p-6">
          <div className="relative flex flex-1 flex-col justify-between gap-3">
            <div className="space-y-3">
              <Badge size="small" variant="outline" className="text-primary">
                Guide
              </Badge>
              <h3 className="-tracking-4 pt-0.5 font-sans text-xl/[1.375rem] font-semibold text-balance text-black md:text-2xl/[1.875rem] dark:text-white">
                {title}
              </h3>
              <h2 className="font-sans text-sm/[1.125rem] text-black md:text-base/[1.375rem] dark:text-neutral-400 [&_b]:md:font-semibold [&_strong]:md:font-semibold">
                {description}
              </h2>
            </div>
          </div>
        </div>
      </div>
    </li>
  );
};
