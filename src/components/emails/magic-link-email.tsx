import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>,
  Hr,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface MagicLinkEmailProps {
  url: string;
}

export const MagicLinkEmail = ({ url }: MagicLinkEmailProps) => {
  const expiryTime = "5 minutes";

  return (
    <Html>
      <Head />
      <Preview>✨ Your magic link to sign in</Preview>
      <Tailwind>
        <Body className="bg-[#ffffff] py-[40px] font-sans">
          <Container className="max-w-[500px] rounded-[0.3rem] border border-[#e6e6e6] bg-[#ffffff] p-[32px]">
            <Section className="mb-[32px]">
              <Text className="m-0 text-[20px] font-medium text-[#141414]">
                ✨ Your magic link is ready
              </Text>
            </Section>

            <Text className="mb-[24px] text-[16px] leading-[24px] text-[#141414]">
              Hey there,
            </Text>

            <Text className="mb-[32px] text-[16px] leading-[24px] text-[#141414]">
              We&apos;ve created a secure, passwordless sign-in link for you.
              Click the button below to access your account instantly.
            </Text>

            <Section className="mb-[32px]">
              <Button
                className="box-border rounded-[0.2rem] bg-[#7857ff] px-[24px] py-[12px] text-center text-[15px] font-medium text-[#f7f7ff] no-underline shadow-sm"
                href={url}
              >
                Sign in to your account →
              </Button>
            </Section>

            <Text className="mb-[8px] text-[14px] leading-[24px] text-[#8d8d91]">
              This link will expire in {expiryTime} and can only be used once
              for security reasons.
            </Text>

            <Text className="mb-[32px] text-[14px] leading-[24px] text-[#8d8d91]">
              If you didn&apos;t request this link, you can safely ignore this
              email. 🔒
            </Text>

            <Hr className="my-[32px] border-[#e6e6e6]" />

            <Text className="mb-[24px] text-[14px] leading-[24px] text-[#141414]">
              Need help? Reply to this email or contact our support team.
            </Text>

            <Text className="text-[14px] leading-[24px] text-[#8d8d91]">
              Alternatively, you can copy and paste this URL into your browser:
            </Text>

            <Text className="text-[14px] leading-[24px] break-all text-[#7857ff]">
              {url}
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

MagicLinkEmail.PreviewProps = {
  url: "https://blocksight.xyz",
} as MagicLinkEmailProps;
