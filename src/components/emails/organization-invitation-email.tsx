import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Link,
  Preview,
  Tailwind,
  Text,
} from "@react-email/components";

interface OrganizationInvitationEmailProps {
  url: string;
  organizationName: string;
  inviterName: string;
}

export const OrganizationInvitationEmail = ({
  url,
  organizationName,
  inviterName,
}: OrganizationInvitationEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>You&apos;ve been invited to join {organizationName}</Preview>
      <Tailwind>
        <Body className="bg-[#F3F4F6] py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[6px] bg-[#FFFFFF] p-[32px] shadow-sm">
            <Heading className="m-0 mb-[24px] text-[24px] font-bold text-[#1F2937]">
              Join {organizationName}
            </Heading>

            <Text className="mb-[24px] text-[16px] text-[#1F2937]">Hello,</Text>

            <Text className="mb-[32px] text-[16px] text-[#1F2937]">
              <span className="font-semibold">{inviterName}</span> has invited
              you to collaborate on{" "}
              <span className="font-semibold">{organizationName}</span>. Join
              the team to start working together.
            </Text>

            <Button
              href={url}
              className="box-border block rounded-[6px] bg-[#9333EA] px-[24px] py-[12px] text-center text-[16px] font-medium text-white no-underline"
            >
              Accept Invitation
            </Button>

            <Text className="mt-[16px] mb-[8px] text-[14px] text-[#6B7280]">
              Or copy and paste this URL into your browser:{" "}
              <Link href={url} className="text-[#9333EA] underline">
                {url}
              </Link>
            </Text>

            <Text className="mb-[32px] text-[14px] font-medium text-[#6B7280]">
              This invitation will expire in 7 days.
            </Text>

            <Hr className="my-[32px] border-[#E5E7EB]" />

            <Text className="text-[14px] text-[#6B7280]">
              If you weren&apos;t expecting this invitation, you can ignore this
              email. If you have any questions, please contact {inviterName}.
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

OrganizationInvitationEmail.PreviewProps = {
  url: "https://blocksight.xyz",
  organizationName: "Acme Inc",
  inviterName: "John Doe",
} as OrganizationInvitationEmailProps;
