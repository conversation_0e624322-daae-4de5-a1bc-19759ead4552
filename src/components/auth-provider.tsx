"use client";

import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { dark } from "@clerk/themes";
import { useTheme } from "next-themes";

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const theme = useTheme();
  return (
    <ClerkProvider
      appearance={{
        baseTheme: theme.theme === "dark" ? dark : undefined,
      }}
    >
      {children}
    </ClerkProvider>
  );
};
