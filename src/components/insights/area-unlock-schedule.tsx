import { format, parse } from "date-fns";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import {
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "../ui/chart";

// Mock unlock schedule data
const unlockScheduleData = [
  {
    date: "01 May 2025",
    company: 200,
    team: 400,
    community: 800,
    foundation: 1200,
    airdrop: 2000,
  },
  {
    date: "01 Sep 2025",
    company: 300,
    team: 600,
    community: 1200,
    foundation: 1800,
    airdrop: 3000,
  },
  {
    date: "01 Jan 2026",
    company: 400,
    team: 800,
    community: 1600,
    foundation: 2400,
    airdrop: 4000,
  },
  {
    date: "01 May 2026",
    company: 500,
    team: 1000,
    community: 2000,
    foundation: 3000,
    airdrop: 5000,
  },
  {
    date: "01 Sep 2026",
    company: 600,
    team: 1200,
    community: 2400,
    foundation: 3600,
    airdrop: 6000,
  },
  {
    date: "01 Jan 2027",
    company: 700,
    team: 1400,
    community: 2800,
    foundation: 4200,
    airdrop: 7000,
  },
  {
    date: "01 May 2027",
    company: 800,
    team: 1600,
    community: 3200,
    foundation: 4800,
    airdrop: 8000,
  },
  {
    date: "01 Sep 2027",
    company: 900,
    team: 1800,
    community: 3600,
    foundation: 5400,
    airdrop: 9000,
  },
  {
    date: "01 Jan 2028",
    company: 1000,
    team: 2000,
    community: 4000,
    foundation: 6000,
    airdrop: 10_000,
  },
];

const areaColors = {
  company: "var(--chart-1)",
  team: "var(--chart-2)",
  community: "var(--chart-3)",
  foundation: "var(--chart-4)",
  airdrop: "var(--chart-5)",
};

const chartConfig = {
  company: { label: "Company", color: areaColors.company },
  team: { label: "Team and Advisors", color: areaColors.team },
  community: { label: "Community", color: areaColors.community },
  foundation: { label: "Foundation", color: areaColors.foundation },
  airdrop: { label: "Community Airdrop", color: areaColors.airdrop },
};

// Formatter for date strings in the mock data
const formatDate = (dateStr: string) => {
  // Parse the date string in 'DD MMM YYYY' format
  const parsed = parse(dateStr, "dd MMM yyyy", new Date());
  return format(parsed, "MMM dd yyyy");
};

export const AreaUnlockScheduleChart = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Unlock Schedule</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[400px] w-full">
          <AreaChart
            data={unlockScheduleData}
            margin={{ top: 24, right: 24, left: 0, bottom: 0 }}
          >
            <defs>
              {Object.entries(areaColors).map(([key, color]) => (
                <linearGradient
                  id={`color-${key}`}
                  key={key}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor={color} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={color} stopOpacity={0.2} />
                </linearGradient>
              ))}
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--border)" />
            <XAxis
              dataKey="date"
              tick={{ fill: "var(--muted-foreground)" }}
              tickLine={false}
              axisLine={false}
              tickFormatter={formatDate}
            />
            <YAxis
              tick={{ fill: "var(--muted-foreground)" }}
              tickLine={false}
              axisLine={false}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  valueFormatter={(v) => `${v.toLocaleString()} ANIME`}
                />
              }
            />
            <Area
              type="stepAfter"
              dataKey="company"
              stackId="1"
              stroke={areaColors.company}
              fill="url(#color-company)"
              name="Company"
            />
            <Area
              type="stepAfter"
              dataKey="team"
              stackId="1"
              stroke={areaColors.team}
              fill="url(#color-team)"
              name="Team and Advisors"
            />
            <Area
              type="stepAfter"
              dataKey="community"
              stackId="1"
              stroke={areaColors.community}
              fill="url(#color-community)"
              name="Community"
            />
            <Area
              type="stepAfter"
              dataKey="foundation"
              stackId="1"
              stroke={areaColors.foundation}
              fill="url(#color-foundation)"
              name="Foundation"
            />
            <Area
              type="stepAfter"
              dataKey="airdrop"
              stackId="1"
              stroke={areaColors.airdrop}
              fill="url(#color-airdrop)"
              name="Community Airdrop"
            />
            <ChartLegend
              content={<ChartLegendContent nameKey="label" />}
              verticalAlign="top"
              align="right"
              iconType="circle"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};
