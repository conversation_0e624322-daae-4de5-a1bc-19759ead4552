"use client";
import { motion, Variants } from "motion/react";
import { useState } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "recharts";
import { noDecimalFormatter } from "~/lib/formatters";
import { AnimatedNumber } from "../ui/animated-number";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "../ui/chart";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";

const itemVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { type: "spring", stiffness: 100, damping: 12 },
  },
};

interface SummaryStatsCardsProps {
  avgTotalTokenSupply?: number | null;
  maxTotalTokenSupply?: number | null;
  investorEquityTokenRatio?: number | null;
  teamAndAdvisorEquityTokenRatio?: number | null;
}

export const SummaryStatsCards = ({
  avgTotalTokenSupply,
  maxTotalTokenSupply,
  investorEquityTokenRatio,
  teamAndAdvisorEquityTokenRatio,
}: SummaryStatsCardsProps) => {
  return (
    <motion.div
      className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4"
      variants={itemVariants} // Apply item variant to the whole section
    >
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-normal">
            Avg. Total Supply
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AnimatedNumber
            value={avgTotalTokenSupply ?? 0}
            formatter={(val) => noDecimalFormatter.format(val)}
            className="text-2xl font-bold transition-all"
          />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-normal">
            Max Total Supply
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AnimatedNumber
            value={maxTotalTokenSupply ?? 0}
            formatter={(val) => noDecimalFormatter.format(val)}
            className="text-2xl font-bold transition-all"
          />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-normal">
            Equity-Token Ratio (Investors)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AnimatedNumber
            value={investorEquityTokenRatio ?? 0}
            formatter={(val) => `${val.toFixed(2)}`}
            className="text-2xl font-bold transition-all"
          />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-normal">
            Equity-Token Ratio (Team + Advisors)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AnimatedNumber
            value={teamAndAdvisorEquityTokenRatio ?? 0}
            formatter={(val) => `${val.toFixed(2)}`}
            className="text-2xl font-bold transition-all"
          />
        </CardContent>
      </Card>
    </motion.div>
  );
};

const tokenDistributionData = [
  {
    name: "Community",
    total: 34.9,
    unlocked: 32.5,
    locked: "-",
    fill: "var(--chart-1)",
  },

  {
    name: "Team",
    total: 11.5,
    unlocked: 10.7,
    locked: "-",
    fill: "var(--chart-3)",
  },
  {
    name: "Advisors",
    total: 11.3,
    unlocked: 10.6,
    locked: "-",
    fill: "var(--chart-4)",
  },
  {
    name: "Investors",
    total: 6.86,
    unlocked: 6.85,
    locked: "-",
    fill: "#fde047",
  }, // approx yellow
  {
    name: "Founders",
    total: 5.79,
    unlocked: 5.79,
    locked: "-",
    fill: "#bef264",
  }, // approx light green
  {
    name: "Treasury",
    total: 4.55,
    unlocked: 4.55,
    locked: "-",
    fill: "#86efac",
  }, // approx darker green
];

const RADIAN = Math.PI / 180;

interface CustomizedLabelProps {
  cx: number;
  cy: number;
  midAngle: number;
  innerRadius: number;
  outerRadius: number;
  value: number;
  index: number; // Keep index here for type completeness, even if unused
}

const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  index: _index,
  value,
}: CustomizedLabelProps) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  // Don't render label if slice is too small
  if (value < 10) {
    return null;
  }

  return (
    <text
      x={x}
      y={y}
      fill="white"
      textAnchor={x > cx ? "start" : "end"}
      dominantBaseline="central"
      fontSize="10px"
      fontWeight="bold"
    >
      {`${value}%`}
    </text>
  );
};

export const TokenDistributionChart = () => {
  // Define insider and outsider groups
  const insiderNames = new Set(["Team", "Advisors", "Investors", "Founders"]);
  const outsiderNames = new Set(["Community", "Treasury"]);

  const insiderData = tokenDistributionData.filter((item) =>
    insiderNames.has(item.name)
  );
  const outsiderData = tokenDistributionData.filter((item) =>
    outsiderNames.has(item.name)
  );

  const insiderTotal = insiderData.reduce(
    (sum, item) => sum + (typeof item.total === "number" ? item.total : 0),
    0
  );
  const outsiderTotal = outsiderData.reduce(
    (sum, item) => sum + (typeof item.total === "number" ? item.total : 0),
    0
  );

  const insiderUnlocked = insiderData.reduce(
    (sum, item) =>
      sum + (typeof item.unlocked === "number" ? item.unlocked : 0),
    0
  );
  const outsiderUnlocked = outsiderData.reduce(
    (sum, item) =>
      sum + (typeof item.unlocked === "number" ? item.unlocked : 0),
    0
  );

  // Normalize allocation totals so they add up to 100%
  const allocationSum = insiderTotal + outsiderTotal;
  const insiderAllocationPercent =
    allocationSum > 0 ? (insiderTotal / allocationSum) * 100 : 0;
  const outsiderAllocationPercent =
    allocationSum > 0 ? (outsiderTotal / allocationSum) * 100 : 0;

  // Highlight state
  const [hovered, setHovered] = useState<string | null>(null);

  // Helper to determine if a slice should be highlighted
  const isHighlighted = (name: string) => {
    if (!hovered) return true;
    if (hovered === "INSIDER") return insiderNames.has(name);
    if (hovered === "OUTSIDER") return outsiderNames.has(name);
    return hovered === name;
  };

  return (
    <motion.div
      variants={itemVariants} // Use item variant for single items
      className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4"
    >
      <Card className="col-span-1 md:col-span-2">
        <CardHeader>
          <CardTitle className="text-lg">Token Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{}}
            className="aspect-square max-h-[500px] w-full"
          >
            <PieChart>
              <Pie
                data={tokenDistributionData}
                dataKey="total"
                nameKey="name"
                cx="50%"
                cy="50%"
                innerRadius={0}
                outerRadius="90%"
                strokeWidth={2}
                labelLine={false}
                label={renderCustomizedLabel}
                isAnimationActive={true}
              >
                {tokenDistributionData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={entry.fill}
                    opacity={isHighlighted(entry.name) ? 1 : 0.1}
                  />
                ))}
              </Pie>
              <ChartTooltip
                cursor={false}
                content={
                  <ChartTooltipContent
                    hideLabel
                    valueFormatter={(value) => `${value}%`}
                  />
                }
              />
            </PieChart>
          </ChartContainer>
        </CardContent>
      </Card>
      <Card className="col-span-1 md:col-span-2">
        <CardContent>
          <Table
            className="data-table relative"
            containerClassName="h-full overflow-y-auto"
          >
            <TableHeader className="bg-muted sticky top-0 z-20 rounded-lg shadow-sm">
              <TableRow>
                <TableHead>Allocation</TableHead>
                <TableHead className="text-right">Total Percentage</TableHead>
                <TableHead className="text-right">Unlock Percentage</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* Insider Allocation Section */}
              <TableRow
                onMouseEnter={() => setHovered("INSIDER")}
                onMouseLeave={() => setHovered(null)}
                style={{
                  cursor: "pointer",
                  background:
                    hovered === "INSIDER" ? "rgba(0,0,0,0.04)" : undefined,
                }}
              >
                <TableCell className="text-lg font-bold">
                  Insider Allocation
                </TableCell>
                <TableCell className="text-right text-lg font-bold">
                  {insiderAllocationPercent.toFixed(2)}%
                </TableCell>
                <TableCell className="text-right text-lg font-bold">
                  {insiderUnlocked.toFixed(2)}%
                </TableCell>
              </TableRow>
              {insiderData.map((item, index) => (
                <TableRow
                  key={"insider-" + index}
                  onMouseEnter={() => setHovered(item.name)}
                  onMouseLeave={() => setHovered(null)}
                  style={{
                    cursor: "pointer",
                    background:
                      hovered === item.name ? "rgba(0,0,0,0.04)" : undefined,
                  }}
                >
                  <TableCell className="pl-4 font-medium">
                    <span
                      className="mr-2 inline-block size-2 shrink-0 rounded-full align-middle"
                      style={{ backgroundColor: item.fill }}
                    />
                    <span className="truncate">{item.name}</span>
                  </TableCell>
                  <TableCell className="text-right">{item.total}%</TableCell>
                  <TableCell className="pr-4 text-right">
                    {item.unlocked}
                    {typeof item.unlocked === "number" ? "%" : ""}
                  </TableCell>
                </TableRow>
              ))}
              {/* Outsider Allocation Section */}
              <TableRow
                onMouseEnter={() => setHovered("OUTSIDER")}
                onMouseLeave={() => setHovered(null)}
                style={{
                  cursor: "pointer",
                  background:
                    hovered === "OUTSIDER" ? "rgba(0,0,0,0.04)" : undefined,
                }}
              >
                <TableCell className="text-lg font-bold">
                  Outsider Allocation
                </TableCell>
                <TableCell className="text-right text-lg font-bold">
                  {outsiderAllocationPercent.toFixed(2)}%
                </TableCell>
                <TableCell className="text-right text-lg font-bold">
                  {outsiderUnlocked.toFixed(2)}%
                </TableCell>
              </TableRow>
              {outsiderData.map((item, index) => (
                <TableRow
                  key={"outsider-" + index}
                  onMouseEnter={() => setHovered(item.name)}
                  onMouseLeave={() => setHovered(null)}
                  style={{
                    cursor: "pointer",
                    background:
                      hovered === item.name ? "rgba(0,0,0,0.04)" : undefined,
                  }}
                >
                  <TableCell className="pl-4 font-medium">
                    <span
                      className="mr-2 inline-block size-2 shrink-0 rounded-full align-middle"
                      style={{ backgroundColor: item.fill }}
                    />
                    <span className="truncate">{item.name}</span>
                  </TableCell>
                  <TableCell className="text-right">{item.total}%</TableCell>
                  <TableCell className="pr-4 text-right">
                    {item.unlocked}
                    {typeof item.unlocked === "number" ? "%" : ""}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </motion.div>
  );
};
