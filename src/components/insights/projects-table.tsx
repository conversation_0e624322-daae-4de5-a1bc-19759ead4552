"use client";

import { useRouter } from "next/navigation";
import { Badge } from "~/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import {
  formatDecimalAsPercentage,
  getNumericValue,
} from "~/lib/allocation-utils";
import { cn } from "~/lib/utils";
import { api } from "~/trpc/react";

const industryColors: Record<string, string> = {
  AI: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  RWA: "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
  L1: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  L2: "bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300",
  DePIN: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300",
  Gaming:
    "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300",
  Marketplace:
    "bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-300",
  Oracle: "bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-300",
  Other: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
};

function formatTokenAmount(amount: bigint | null): string {
  if (!amount) return "N/A";

  const num = Number(amount);
  if (num >= 1e12) return `${(num / 1e12).toFixed(1)}T`;
  if (num >= 1e9) return `${(num / 1e9).toFixed(1)}B`;
  if (num >= 1e6) return `${(num / 1e6).toFixed(1)}M`;
  if (num >= 1e3) return `${(num / 1e3).toFixed(1)}K`;
  return num.toLocaleString();
}

export function ProjectsTable() {
  const router = useRouter();
  const { data: projects, isLoading, error } = api.project.getAll.useQuery();

  const handleRowClick = (projectId: string) => {
    router.push(`/insights/${projectId}`);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="text-muted-foreground py-8 text-center">
          Loading projects...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="py-8 text-center text-red-500">
          Error loading projects: {error.message}
        </div>
      </div>
    );
  }

  if (!projects || projects.length === 0) {
    return (
      <div className="space-y-4">
        <div className="text-muted-foreground py-8 text-center">
          No projects found.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader className="bg-muted sticky top-0 z-20">
          <TableRow>
            <TableHead className="w-[200px]">
              <div className="flex items-center gap-1">Project</div>
            </TableHead>
            <TableHead>Industry</TableHead>
            <TableHead>
              <div className="flex items-center gap-1">Total Supply</div>
            </TableHead>
            <TableHead>
              <div className="flex items-center gap-1">Insider %</div>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {projects.map((project) => (
            <TableRow
              key={project.id}
              className="cursor-pointer"
              onClick={() => handleRowClick(project.id)}
            >
              <TableCell>
                <div>
                  <div className="hover:text-primary font-medium transition-colors">
                    {project.name || "Unnamed Project"}
                  </div>
                  <div className="text-muted-foreground text-sm">
                    {project.symbol || "N/A"}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                {project.industry && (
                  <Badge
                    variant="secondary"
                    className={cn(
                      "font-normal",
                      industryColors[project.industry] || industryColors.Other
                    )}
                  >
                    {project.industry}
                  </Badge>
                )}
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">
                    {formatTokenAmount(project.totalTokenSupply)}
                  </div>
                  <div className="text-muted-foreground text-sm">
                    Circulating:{" "}
                    {formatTokenAmount(project.circulatingTokenSupply)}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-red-500" />
                  <span className="font-medium">
                    {project.insiderAllocationPoolDecimal === null
                      ? "N/A"
                      : formatDecimalAsPercentage(
                          getNumericValue(project.insiderAllocationPoolDecimal)
                        )}
                  </span>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
