"use client";

import { cva, type VariantProps } from "class-variance-authority";
import { XIcon } from "lucide-react";
import * as React from "react";
import { cn } from "~/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center gap-1.5 rounded-full text-xs font-medium ring-1 ring-inset transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ",
  {
    variants: {
      variant: {
        default: "bg-surface-200/10 text-foreground-light ring-surface-200",
        warning: "bg-warning/10 text-warning-600 ring-warning/30",
        success: "bg-primary/10 text-foreground ring-primary/30",
        destructive:
          "bg-destructive/10 text-destructive-600 ring-destructive/30",
        primary: "bg-primary/10 text-foreground ring-primary/30",
        secondary:
          "bg-secondary/10 text-secondary-foreground ring-secondary/30",
        outline: "bg-background text-foreground-light ring-border",
      },
      size: {
        small: "px-2 py-1 text-xs",
        default: "px-2.5 py-1 text-sm",
        large: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  onClear?: () => void;
  clearable?: boolean;
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  (
    {
      className,
      variant,
      size,
      children,
      clearable = false,
      onClear,
      ...props
    },
    ref
  ) => {
    const handleClear = (e: React.MouseEvent) => {
      e.stopPropagation();
      onClear?.();
    };

    return (
      <div
        ref={ref}
        className={cn(badgeVariants({ variant, size }), className)}
        {...props}
      >
        {children}
        {clearable && (
          <button
            type="button"
            className="hover:bg-surface-300/20 focus:ring-ring ml-0.5 rounded-full focus:ring-2 focus:ring-offset-2 focus:outline-none"
            onClick={handleClear}
            aria-label="Clear"
          >
            <XIcon size={12} className="text-current" />
          </button>
        )}
      </div>
    );
  }
);

Badge.displayName = "Badge";

export { Badge, badgeVariants };
