"use client";

import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import { forwardRef, useCallback, useEffect, useRef, useState } from "react";
import { NumericFormat, NumericFormatProps } from "react-number-format";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { cn } from "~/lib/utils";

export interface NumberInputProps
  extends Omit<NumericFormatProps, "value" | "onChange"> {
  stepper?: number;
  thousandSeparator?: string;
  placeholder?: string;
  min?: number;
  max?: number;
  value?: number;
  suffix?: string;
  prefix?: string;
  onChange?: (value: number | undefined) => void;
  fixedDecimalScale?: boolean;
  decimalScale?: number;
  className?: string;
}

// Add a stable custom input component to avoid remounting on each render
const CustomNumericInput = forwardRef<
  HTMLInputElement,
  React.ComponentProps<typeof Input>
>((props, ref) => {
  return <Input {...props} ref={ref} className={cn(props.className, "pr-8")} />;
});
CustomNumericInput.displayName = "CustomNumericInput";

export const NumberInput = forwardRef<HTMLInputElement, NumberInputProps>(
  (
    {
      stepper,
      thousandSeparator = ",",
      placeholder,
      min = -Infinity,
      max = Infinity,
      onChange: onValueChange,
      fixedDecimalScale = false,
      decimalScale = 0,
      suffix,
      prefix,
      value: controlledValue,
      className: inputClassName,
      ...rest
    },
    ref
  ) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const [innerValue, setInnerValue] = useState<number | undefined>(
      controlledValue
    );
    const prevControlledRef = useRef<number | undefined>(controlledValue);

    // Sync with controlled value when it changes externally
    useEffect(() => {
      if (controlledValue !== prevControlledRef.current) {
        setInnerValue(controlledValue);
        prevControlledRef.current = controlledValue;
      }
    }, [controlledValue]);

    const mergeRef = useCallback(
      (node: HTMLInputElement) => {
        inputRef.current = node;
        if (typeof ref === "function") {
          ref(node);
        } else if (ref) {
          (ref as React.MutableRefObject<HTMLInputElement | null>).current =
            node;
        }
      },
      [ref]
    );

    const handleValueChange = useCallback(
      (values: { value: string; floatValue: number | undefined }) => {
        setInnerValue(values.floatValue);
        // propagate change on every keystroke
        onValueChange?.(values.floatValue);
      },
      [onValueChange]
    );

    const handleBlur = useCallback(() => {
      if (onValueChange) {
        onValueChange(innerValue);
      }
    }, [onValueChange, innerValue]);

    const handleIncrement = useCallback(() => {
      const current = innerValue ?? 0;
      const next = Math.min(current + (stepper ?? 1), max);
      setInnerValue(next);
    }, [innerValue, stepper, max]);

    const handleDecrement = useCallback(() => {
      const current = innerValue ?? 0;
      const next = Math.max(current - (stepper ?? 1), min);
      setInnerValue(next);
    }, [innerValue, stepper, min]);

    return (
      <div className="relative flex items-center">
        <NumericFormat
          getInputRef={mergeRef}
          value={innerValue}
          onValueChange={handleValueChange}
          onBlur={handleBlur}
          thousandSeparator={thousandSeparator}
          decimalScale={decimalScale}
          fixedDecimalScale={fixedDecimalScale}
          allowNegative={min < 0}
          isAllowed={(values) => {
            const { floatValue } = values;
            if (floatValue === undefined) return true;
            return floatValue >= min && floatValue <= max;
          }}
          valueIsNumericString
          suffix={suffix}
          prefix={prefix}
          customInput={CustomNumericInput}
          className={inputClassName}
          placeholder={placeholder}
          {...rest}
        />
        <div className="absolute top-1/2 right-1 flex h-full -translate-y-1/2 flex-col items-center justify-center">
          <Button
            aria-label="Increase value"
            className="text-muted-foreground hover:bg-muted h-1/3 rounded-b-none border-0 px-1 hover:rounded-sm"
            variant="ghost"
            size="icon"
            onClick={handleIncrement}
            tabIndex={-1}
            type="button"
          >
            <ChevronUpIcon size={15} />
          </Button>
          <Button
            aria-label="Decrease value"
            className="text-muted-foreground hover:bg-muted h-1/3 rounded-t-none border-0 px-1 hover:rounded-sm"
            variant="ghost"
            size="icon"
            onClick={handleDecrement}
            tabIndex={-1}
            type="button"
          >
            <ChevronDownIcon size={15} />
          </Button>
        </div>
      </div>
    );
  }
);

NumberInput.displayName = "NumberInput";
