"use client";

import { useAuth, useOrganization, useUser } from "@clerk/nextjs";
import {
  ChevronDownIcon,
  MailIcon,
  MoreHorizontalIcon,
  RefreshCwIcon,
  SearchIcon,
  Trash2Icon,
  UserRoundMinusIcon,
  UserRoundPlusIcon,
  UsersIcon,
} from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { useState } from "react";
import { toast } from "sonner";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { Skeleton } from "~/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Textarea } from "~/components/ui/textarea";

// Inline invite form with motion animation
function InviteForm({
  onInvite,
  onClose,
}: {
  onInvite: (emails: string[], role: string) => Promise<void>;
  onClose: () => void;
}) {
  const [emails, setEmails] = useState("");
  const [role, setRole] = useState("org:member");
  const [roleOpen, setRoleOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleInvite = async () => {
    if (!emails.trim()) {
      toast.error("Please enter at least one email address");
      return;
    }

    setIsLoading(true);
    try {
      const emailList = emails
        .split(/[ ,\s]+/)
        .map((email) => email.trim())
        .filter((email) => email.length > 0);

      await onInvite(emailList, role);
      toast.success(
        `Invitation${emailList.length > 1 ? "s" : ""} sent successfully!`
      );
      setEmails("");
      onClose();
    } catch (error) {
      toast.error("Failed to send invitations");
      console.error("Invitation error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: "auto", opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      transition={{ duration: 0.2 }}
      className="bg-background overflow-hidden rounded-md border p-4 shadow-sm"
    >
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="emails">Email addresses</Label>
          <Textarea
            id="emails"
            placeholder="<EMAIL>, <EMAIL>"
            value={emails}
            onChange={(e) => setEmails(e.target.value)}
            rows={3}
            className="resize-none"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="role">Role</Label>
          <Popover open={roleOpen} onOpenChange={setRoleOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="flex w-fit items-center gap-2"
              >
                {role === "org:admin" ? "Admin" : "Member"}
                <ChevronDownIcon className="size-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-32 p-0" align="start">
              <div className="flex flex-col">
                <Button
                  variant="ghost"
                  size="sm"
                  className="justify-start"
                  onClick={() => {
                    setRole("org:member");
                    setRoleOpen(false);
                  }}
                >
                  Member
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="justify-start"
                  onClick={() => {
                    setRole("org:admin");
                    setRoleOpen(false);
                  }}
                >
                  Admin
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
      <div className="mt-4 flex justify-end gap-2">
        <Button
          variant="outline"
          onClick={onClose}
          disabled={isLoading}
          size="sm"
        >
          Cancel
        </Button>
        <Button onClick={handleInvite} disabled={isLoading} size="sm">
          {isLoading ? "Sending..." : "Send"}
        </Button>
      </div>
    </motion.div>
  );
}

// Helper functions
function normalizeRole(role: string) {
  return role.replace(/^org:/, "");
}

function formatRole(rawRole: string) {
  const role = normalizeRole(rawRole);
  switch (role) {
    case "admin": {
      return "Admin";
    }
    case "member":
    case "basic_member": {
      return "Member";
    }
    default: {
      return role;
    }
  }
}

interface TeamInviteModalProps {
  children?: React.ReactNode;
  defaultOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function InviteModal({
  children,
  defaultOpen = false,
  onOpenChange,
}: TeamInviteModalProps) {
  const { organization, memberships, invitations, isLoaded } = useOrganization({
    memberships: {
      infinite: true,
    },
    invitations: {
      infinite: true,
    },
  });

  const { orgRole } = useAuth();
  const { user: currentUser } = useUser();

  const [searchTerm, setSearchTerm] = useState("");
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [open, setOpen] = useState(defaultOpen);

  const isCurrentUserAdmin = orgRole === "org:admin";
  const canManageMembers = isCurrentUserAdmin;

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    onOpenChange?.(newOpen);
  };

  const handleInviteMembers = async (emails: string[], role: string) => {
    if (!organization) return;

    for (const email of emails) {
      try {
        const roleKey = role.startsWith("org:") ? role : `org:${role}`;

        await organization.inviteMembers({
          emailAddresses: [email],
          role: roleKey,
        });
        invitations?.revalidate?.();
      } catch (error) {
        console.error(`Failed to invite ${email}:`, error);
        throw error;
      }
    }
  };

  const handleRemoveMember = async (userId: string) => {
    if (!organization) return;

    try {
      await organization.removeMember(userId);
      memberships?.revalidate?.();
      toast.success("Member removed successfully");
    } catch (error) {
      toast.error("Failed to remove member");
      console.error("Remove member error:", error);
    }
  };

  const handleUpdateMemberRole = async (userId: string, role: string) => {
    if (!organization) return;

    try {
      await organization.updateMember({
        userId,
        role,
      });
      memberships?.revalidate?.();
      toast.success("Member role updated successfully");
    } catch (error) {
      toast.error("Failed to update member role");
      console.error("Update member role error:", error);
    }
  };

  const handleRevokeInvitation = async (invitationId: string) => {
    if (!organization) return;

    try {
      const invitation = invitations?.data?.find(
        (inv) => inv.id === invitationId
      );
      if (invitation) {
        await invitation.revoke();
        invitations?.revalidate?.();
        toast.success("Invitation revoked successfully");
      }
    } catch (error) {
      toast.error("Failed to revoke invitation");
      console.error("Revoke invitation error:", error);
    }
  };

  const handleResendInvitation = async (invitationId: string) => {
    if (!organization) return;

    try {
      const invitation = invitations?.data?.find(
        (inv) => inv.id === invitationId
      );
      if (invitation) {
        // Resend the invitation using the same email and role
        await organization.inviteMembers({
          emailAddresses: [invitation.emailAddress],
          role: invitation.role,
        });
        toast.success("Invitation resent successfully");
      }
    } catch (error) {
      toast.error("Failed to resend invitation");
      console.error("Resend invitation error:", error);
    }
  };

  const filteredMembers = memberships?.data?.filter((membership) => {
    const user = membership.publicUserData;
    const searchLower = searchTerm.toLowerCase();
    return (
      user?.firstName?.toLowerCase().includes(searchLower) ||
      user?.lastName?.toLowerCase().includes(searchLower) ||
      user?.identifier?.toLowerCase().includes(searchLower)
    );
  });

  const filteredInvitations = invitations?.data?.filter((invitation) => {
    const searchLower = searchTerm.toLowerCase();
    return invitation.emailAddress?.toLowerCase().includes(searchLower);
  });

  const membersCount = memberships?.data?.length || 0;
  const invitationsCount = invitations?.data?.length || 0;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UsersIcon className="h-5 w-5" />
            Team Management
          </DialogTitle>
          <DialogDescription>
            {canManageMembers
              ? "Manage your team members and send invitations to new members."
              : "View your team members. Only admins can manage members and send invitations."}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {isLoaded ? (
            <Tabs defaultValue="members" className="w-full">
              <TabsList className="grid w-full grid-cols-2" variant="underline">
                <TabsTrigger
                  value="members"
                  variant="underline"
                  className="flex items-center gap-2"
                >
                  Members
                  <span className="bg-muted ml-1 rounded-full px-2 py-0.5 text-xs">
                    {membersCount}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="invitations"
                  variant="underline"
                  className="flex items-center gap-2"
                >
                  Invitations
                  {invitations?.count && invitations.count > 0 ? (
                    <span className="bg-muted ml-1 rounded-full px-2 py-0.5 text-xs">
                      {invitationsCount}
                    </span>
                  ) : null}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="members" className="mt-4 space-y-4">
                {/* Search + Invite */}
                <div className="flex items-start gap-2">
                  <div className="relative flex-1">
                    <SearchIcon className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                    <Input
                      placeholder="Search members"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-9"
                    />
                  </div>
                  {canManageMembers && (
                    <Button
                      variant="outline"
                      onClick={() => setShowInviteForm((prev) => !prev)}
                      className="gap-2 whitespace-nowrap"
                    >
                      Invite
                    </Button>
                  )}
                </div>

                {/* Invite Form */}
                <AnimatePresence>
                  {showInviteForm && canManageMembers && (
                    <InviteForm
                      key="invite-form"
                      onInvite={handleInviteMembers}
                      onClose={() => setShowInviteForm(false)}
                    />
                  )}
                </AnimatePresence>

                {/* Members List */}
                <div className="max-h-96 space-y-0 divide-y overflow-y-auto">
                  {filteredMembers && filteredMembers.length > 0 ? (
                    filteredMembers.map((membership) => {
                      const user = membership.publicUserData;
                      const isCurrentUser =
                        membership.publicUserData?.userId === currentUser?.id;

                      return (
                        <div
                          key={membership.id}
                          className="flex items-center justify-between py-3"
                        >
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={user?.imageUrl} />
                              <AvatarFallback className="text-xs">
                                {user?.firstName?.[0]}
                                {user?.lastName?.[0]}
                              </AvatarFallback>
                            </Avatar>
                            <div className="space-y-0.5">
                              <div className="flex items-center gap-2">
                                <p className="text-sm leading-none font-medium">
                                  {user?.firstName} {user?.lastName}
                                  {isCurrentUser && (
                                    <span className="text-muted-foreground ml-1">
                                      (You)
                                    </span>
                                  )}
                                </p>
                              </div>
                              <p className="text-muted-foreground text-xs">
                                {user?.identifier}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="primary" className="text-xs">
                              {formatRole(membership.role)}
                            </Badge>
                            {!isCurrentUser && canManageMembers && (
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8"
                                  >
                                    <MoreHorizontalIcon className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() => {
                                      const current = normalizeRole(
                                        membership.role
                                      );
                                      const nextRole =
                                        current === "admin"
                                          ? "org:member"
                                          : "org:admin";
                                      const userId =
                                        membership.publicUserData?.userId;
                                      if (userId) {
                                        handleUpdateMemberRole(
                                          userId,
                                          nextRole
                                        );
                                      }
                                    }}
                                  >
                                    {normalizeRole(membership.role) ===
                                    "admin" ? (
                                      <>
                                        <UserRoundMinusIcon className="h-4 w-4" />
                                        Remove admin
                                      </>
                                    ) : (
                                      <>
                                        <UserRoundPlusIcon className="h-4 w-4" />
                                        Make admin
                                      </>
                                    )}
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      const userId =
                                        membership.publicUserData?.userId;
                                      if (userId) {
                                        handleRemoveMember(userId);
                                      }
                                    }}
                                    className="text-destructive"
                                  >
                                    <Trash2Icon className="text-destructive h-4 w-4" />
                                    Remove member
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            )}
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8">
                      <p className="text-muted-foreground text-sm">
                        No members found
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="invitations" className="mt-4 space-y-4">
                <div className="max-h-96 space-y-0 divide-y overflow-y-auto">
                  {filteredInvitations && filteredInvitations.length > 0 ? (
                    filteredInvitations.map((invitation) => (
                      <div
                        key={invitation.id}
                        className="flex items-center justify-between py-3"
                      >
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback className="text-xs">
                              <MailIcon className="h-4 w-4" />
                            </AvatarFallback>
                          </Avatar>
                          <div className="space-y-1">
                            <p className="text-sm leading-none font-medium">
                              {invitation.emailAddress}
                            </p>
                            <p className="text-muted-foreground text-xs">
                              Invitation pending
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="primary" className="text-xs">
                            {formatRole(invitation.role)}
                          </Badge>
                          {canManageMembers && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8"
                                >
                                  <MoreHorizontalIcon className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleResendInvitation(invitation.id)
                                  }
                                >
                                  <RefreshCwIcon className="h-4 w-4" />
                                  Resend invitation
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleRevokeInvitation(invitation.id)
                                  }
                                  className="text-destructive"
                                >
                                  <Trash2Icon className="text-destructive h-4 w-4" />
                                  Revoke invitation
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8">
                      <div className="text-muted-foreground text-sm">
                        No invitations found
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          ) : (
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <div className="space-y-3">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-48" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
