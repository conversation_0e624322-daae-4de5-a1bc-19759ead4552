"use client";

import {
  ArrowUpIcon,
  FileArchiveIcon,
  FileIcon,
  FileSpreadsheetIcon,
  FileTextIcon,
  Loader2Icon,
  XIcon,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { Button } from "~/components/ui/button";
import { Progress } from "~/components/ui/progress";
import { APP_NAME, cn } from "~/lib/utils";

// Constants for multipart upload
const CHUNK_SIZE = 10 * 1024 * 1024; // 10MB chunks
const FILE_SIZE_THRESHOLD = 100 * 1024 * 1024; // 100MB threshold to use multipart
const MAX_CONCURRENT_UPLOADS = 10; // Increased from 5 to 10 for higher concurrency

// Function to upload a large file in parts
const uploadLargeFile = async (
  file: File,
  abortController?: AbortController
): Promise<{ success: boolean; url?: string; error?: string }> => {
  try {
    // Create a local abort controller if none was provided
    const controller = abortController || new AbortController();
    const signal = controller.signal;

    // Check if already aborted
    if (signal.aborted) {
      return { success: false, error: "Upload was cancelled" };
    }

    // Calculate the number of parts needed
    const partCount = Math.ceil(file.size / CHUNK_SIZE);

    // Initialize multipart upload
    const initResponse = await fetch("/api/s3/multipart-upload", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        fileName: file.name,
        contentType: file.type,
        partCount,
        action: "init",
      }),
      signal, // Add signal to the fetch request
    });

    if (!initResponse.ok) {
      const error = await initResponse.text();
      return {
        success: false,
        error: `Failed to initialize multipart upload: ${error}`,
      };
    }

    const { uploadId, presignedUrls, key } = await initResponse.json();
    console.log(
      `Multipart upload initialized: File key ${key}, uploadId: ${uploadId}`
    );

    // Upload each part
    const parts: { ETag: string; PartNumber: number }[] = [];

    // Track progress for UI updates
    let completedParts = 0;

    // Function to update progress - will be passed to handleUpload
    const updateProgress = (partIndex: number) => {
      completedParts++;
      // Calculate progress percentage based on completed parts
      const progressPercent = Math.round((completedParts / partCount) * 100);
      console.log(
        `Upload progress: ${progressPercent}% (Part ${partIndex + 1}/${partCount})`
      );
      // Progress updates will be handled by the caller via the returned Promise
    };

    // Use a queue-based approach with limited concurrency
    const uploadPart = async (
      partIndex: number,
      retryCount = 0
    ): Promise<void> => {
      // Check if aborted before starting this part
      if (signal.aborted) {
        return;
      }

      try {
        const start = partIndex * CHUNK_SIZE;
        const end = Math.min(start + CHUNK_SIZE, file.size);
        const chunk = file.slice(start, end);

        // Use the main abort controller for this request
        const chunkResponse = await fetch(presignedUrls[partIndex], {
          method: "PUT",
          body: chunk,
          signal, // Use the same signal for all requests
        });

        if (!chunkResponse.ok) {
          throw new Error(
            `Failed to upload part ${partIndex + 1} with status ${chunkResponse.status}`
          );
        }

        const etag = chunkResponse.headers.get("ETag");
        if (!etag) {
          throw new Error(`No ETag returned for part ${partIndex + 1}`);
        }

        parts.push({
          ETag: etag.replaceAll('"', ""),
          PartNumber: partIndex + 1,
        });

        // Update progress
        updateProgress(partIndex);
      } catch (error) {
        console.log(error);
        // If aborted, don't retry
        if (signal.aborted) {
          return;
        }

        // Retry logic for failed uploads
        if (!signal.aborted && retryCount < 3) {
          console.warn(
            `Retrying part ${partIndex + 1}, attempt ${retryCount + 1}`
          );
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 * Math.pow(2, retryCount))
          ); // Exponential backoff
          await uploadPart(partIndex, retryCount + 1);
        } else {
          throw error; // Rethrow after max retries
        }
      }
    };

    // Queue-based implementation for controlled parallelism
    const queue = Array.from({ length: partCount }, (_, i) => i); // Create array of part indices [0, 1, 2, ...]
    let activeUploads = 0;
    let error: Error | undefined = undefined;

    // Create a promise that will resolve when all parts are uploaded
    return new Promise((resolve) => {
      // Function to process the next item in the queue
      const processQueue = async () => {
        // Check if aborted
        if (signal.aborted) {
          resolve({ success: false, error: "Upload was cancelled" });
          return;
        }

        if (queue.length === 0) {
          // If queue is empty and no active uploads, we're done
          if (activeUploads === 0) {
            if (error) {
              resolve({
                success: false,
                error: error.message,
              });
            } else {
              // Continue with completion
              finishUpload();
            }
          }
          return;
        }

        // If we have capacity and items in queue, process next item
        while (queue.length > 0 && activeUploads < MAX_CONCURRENT_UPLOADS) {
          // Check if aborted before starting a new upload
          if (signal.aborted) {
            resolve({ success: false, error: "Upload was cancelled" });
            return;
          }

          const partIndex = queue.shift()!;
          activeUploads++;

          // Process this part and then check queue again when done
          uploadPart(partIndex)
            .then(() => {
              activeUploads--;
              processQueue(); // Process more from queue
            })
            .catch((error_) => {
              console.error(`Error uploading part ${partIndex}:`, error_);
              activeUploads--;

              // If aborted, stop processing
              if (signal.aborted || error_?.message?.includes("cancelled")) {
                resolve({ success: false, error: "Upload was cancelled" });
                return;
              }

              error = error_;
              processQueue(); // Continue processing queue despite error
            });
        }
      };

      // Function to finish the upload after all parts are processed
      const finishUpload = async () => {
        try {
          // Check if aborted before completing
          if (signal.aborted) {
            resolve({ success: false, error: "Upload was cancelled" });
            return;
          }

          // Ensure parts are sorted by part number as S3 requires
          parts.sort((a, b) => a.PartNumber - b.PartNumber);

          console.log(
            `Completing multipart upload for key: ${key}, uploadId: ${uploadId}, with ${parts.length} parts`
          );

          // Complete the multipart upload
          const completeResponse = await fetch("/api/s3/multipart-upload", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              uploadId,
              fileName: key,
              parts,
              action: "complete",
            }),
            signal, // Add signal to the fetch request
          });

          if (!completeResponse.ok) {
            const errorText = await completeResponse.text();
            resolve({
              success: false,
              error: `Failed to complete multipart upload: ${errorText}`,
            });
            return;
          }

          const completeData = await completeResponse.json();
          resolve({ success: true, url: completeData.url });
        } catch (error_) {
          // Check if aborted
          if (signal.aborted) {
            resolve({ success: false, error: "Upload was cancelled" });
            return;
          }

          console.error("Error completing multipart upload:", error_);
          resolve({
            success: false,
            error:
              error_ instanceof Error
                ? error_.message
                : "Unknown error occurred",
          });
        }
      };

      // Add an abort listener to clean up
      signal.addEventListener("abort", () => {
        // If we have an uploadId, we should try to abort the multipart upload on S3
        if (uploadId) {
          // Best effort to abort the multipart upload on S3
          fetch("/api/s3/multipart-upload", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              uploadId,
              fileName: key,
              action: "abort",
            }),
          }).catch((error_) =>
            console.error("Failed to abort multipart upload:", error_)
          );
        }

        resolve({ success: false, error: "Upload was cancelled" });
      });

      // Start processing the queue
      processQueue();
    });
  } catch (error) {
    console.error("Error in multipart upload:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};

// Enhanced upload function that decides whether to use multipart or single upload
const uploadToS3 = async (
  file: File,
  abortController?: AbortController
): Promise<{ success: boolean; url?: string; error?: string }> => {
  try {
    // Create a local abort controller if none was provided
    const controller = abortController || new AbortController();
    const signal = controller.signal;

    // Check if already aborted
    if (signal.aborted) {
      return { success: false, error: "Upload was cancelled" };
    }

    const response = await fetch("/api/s3/presigned-upload", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        fileName: file.name,
        contentType: file.type,
      }),
      signal, // Add signal to the fetch request
    });

    if (!response.ok) {
      const error = await response.text();
      return { success: false, error };
    }

    const { presignedUrl, url } = await response.json();

    // Upload the file using the presigned URL
    const uploadResponse = await fetch(presignedUrl, {
      method: "PUT",
      body: file,
      headers: {
        "Content-Type": file.type,
      },
      signal,
    });

    if (!uploadResponse.ok) {
      return { success: false, error: "Failed to upload file to S3" };
    }

    return { success: true, url };
  } catch (error) {
    // Check if it was cancelled
    if (error instanceof Error && error.name === "AbortError") {
      return { success: false, error: "Upload was cancelled" };
    }

    console.error("Error uploading to S3:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};

// Add the FileObject type definition if it doesn't exist
type FileStatus = "pending" | "uploading" | "completed" | "error";

interface FileObject {
  id: string;
  file: File;
  status: FileStatus;
  percentComplete?: number;
  progress?: number;
  url?: string;
  error?: string;
}

const ACCEPTED_MIME_TYPES = {
  "text/csv": [".csv"],
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
    ".xlsx",
  ],
  "application/vnd.ms-excel": [".xls"],
  "application/pdf": [".pdf"],
  "application/zip": [".zip"],
  "application/x-zip-compressed": [".zip"], // Additional MIME type for zip files
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return (
    Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  );
};

const getFileFormat = (file: File) => {
  const extension =
    file.name.split(".").pop()?.toUpperCase() ||
    file.type.split("/")[1]?.toUpperCase();
  return extension;
};

const getFileIcon = (fileName: string) => {
  const extension = fileName.split(".").pop()?.toLowerCase();

  switch (extension) {
    case "csv":
    case "xlsx":
    case "xls": {
      return <FileSpreadsheetIcon className="text-muted-foreground h-6 w-6" />;
    }
    case "pdf": {
      return <FileTextIcon className="text-muted-foreground h-6 w-6" />;
    }
    case "zip": {
      return <FileArchiveIcon className="text-muted-foreground h-6 w-6" />;
    }
    default: {
      return <FileIcon className="text-muted-foreground h-6 w-6" />;
    }
  }
};

// Fix the hasUploadableFiles function with proper typing
const hasUploadableFiles = (files: FileObject[]) => {
  return files.some((f) => f.status === "pending" || f.status === "error");
};

export default function FileUploader() {
  const [files, setFiles] = useState<FileObject[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  // State to track if we should show the navigation warning modal
  const [showNavigationWarning, setShowNavigationWarning] = useState(false);
  // We don't need to store the event since we're not using it

  // Function to check if there are active uploads
  const hasActiveUploads = useCallback(() => {
    return files.some((f) => f.status === "uploading") || isUploading;
  }, [files, isUploading]);

  // Add useEffect to handle the beforeunload event
  useEffect(() => {
    // Function to show warning when user tries to leave during active uploads
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasActiveUploads()) {
        // Standard way to show a confirmation dialog
        // The message text is often ignored by browsers and replaced with a generic message
        const message =
          "You have uploads in progress. Are you sure you want to leave?";
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
    };

    // Add the event listener
    window.addEventListener("beforeunload", handleBeforeUnload);

    // Clean up the event listener when component unmounts
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [hasActiveUploads]);

  // Handle in-app navigation attempts
  useEffect(() => {
    const handlePopState = (e: PopStateEvent) => {
      if (hasActiveUploads()) {
        // Block navigation and show a warning
        e.preventDefault();
        setShowNavigationWarning(true);
        // Push another state to prevent immediate navigation
        globalThis.history.pushState(undefined, "", globalThis.location.href);
      }
    };

    // Listen for the popstate event (back/forward navigation)
    globalThis.addEventListener("popstate", handlePopState);

    // Push an initial state
    globalThis.history.pushState(undefined, "", globalThis.location.href);

    return () => {
      globalThis.removeEventListener("popstate", handlePopState);
    };
  }, [hasActiveUploads]);

  // Function to handle when user confirms they want to leave
  const confirmNavigation = useCallback(() => {
    setShowNavigationWarning(false);
    // Allow the navigation to happen
    const handleBeforeUnloadTemp = () => {}; // Empty function for removeEventListener
    window.removeEventListener("beforeunload", handleBeforeUnloadTemp);
    globalThis.history.back();
  }, []);

  // Function to handle when user cancels navigation
  const cancelNavigation = useCallback(() => {
    setShowNavigationWarning(false);
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    // Add new files to the list
    setFiles((prev) => [
      ...prev,
      ...acceptedFiles.map((file) => ({
        id: `${file.name}-${Date.now()}`,
        file,
        status: "pending" as FileStatus,
        progress: 0, // Initialize progress to 0
      })),
    ]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
    accept: ACCEPTED_MIME_TYPES,
    onDropRejected: (fileRejections) => {
      for (const rejection of fileRejections) {
        toast.error(`File "${rejection.file.name}" was rejected`, {
          description: "Only CSV, XLSX, PDF, and ZIP files are supported.",
        });
      }
    },
  });

  const handleUpload = async (fileId: string) => {
    const fileObj = files.find((f) => f.id === fileId);
    if (!fileObj) return;
    const fileToUpload = fileObj.file;

    // Create an AbortController for this upload
    const abortController = new AbortController();

    // Store the AbortController in a ref or state so we can access it later
    // We'll use a Map to store multiple controllers if needed
    setAbortControllers((prev) => new Map(prev).set(fileId, abortController));

    // Set initial state for upload
    setFiles((prev) =>
      prev.map((f) =>
        f.id === fileId
          ? {
              ...f,
              status: "uploading",
              progress: 0,
              percentComplete: 0,
            }
          : f
      )
    );

    // Determine if the file is large based on the fileToUpload
    const isLargeFile = fileToUpload.size > FILE_SIZE_THRESHOLD;
    let progressInterval: ReturnType<typeof setInterval> | undefined;

    // Create a function to update the UI with progress (used for large files)
    const updateUIProgress = (progress: number) => {
      setFiles((prev) =>
        prev.map((f) =>
          f.id === fileId
            ? {
                ...f,
                progress,
                percentComplete: progress,
              }
            : f
        )
      );
    };

    // Define the progress listener outside the if block so it's in scope for the whole function
    const progressListener = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.fileId === fileId) {
        updateUIProgress(customEvent.detail.progress);
      }
    };

    // For smaller files, we'll use a simulated progress
    if (isLargeFile) {
      // For large files, set up a progress listener

      // Add event listener for progress updates
      globalThis.addEventListener("upload-progress", progressListener);

      // Set initial progress
      updateUIProgress(0);
    } else {
      progressInterval = setInterval(() => {
        setFiles((prev) => {
          const fileToUpdate = prev.find((f) => f.id === fileId);
          if (
            fileToUpdate &&
            fileToUpdate.status === "uploading" &&
            (fileToUpdate.progress ?? 0) < 95
          ) {
            const newProgress = Math.min((fileToUpdate.progress ?? 0) + 5, 95);
            return prev.map((f) =>
              f.id === fileId
                ? {
                    ...f,
                    progress: newProgress,
                    percentComplete: newProgress,
                  }
                : f
            );
          }
          return prev;
        });
      }, 500);
    }

    try {
      // For large files, we'll create a custom event system to track progress
      if (isLargeFile) {
        // Set up a progress tracking system using custom events
        let lastProgressUpdate = Date.now();
        let lastProgress = 0;

        // Create a wrapper around the original uploadLargeFile to track progress
        const trackingUploadLargeFile = async (file: File) => {
          // Create a progress tracking interval
          const trackingInterval = setInterval(() => {
            // Check if we've received progress updates
            const now = Date.now();
            if (now - lastProgressUpdate > 5000 && lastProgress < 95) {
              // If no updates for 5 seconds, increment slightly to show activity
              lastProgress = Math.min(lastProgress + 2, 95);
              globalThis.dispatchEvent(
                new CustomEvent("upload-progress", {
                  detail: { fileId, progress: lastProgress },
                })
              );
            }
          }, 5000);

          // Set up a listener for the console.log messages from uploadLargeFile
          const originalConsoleLog = console.log;
          console.log = function (...args) {
            originalConsoleLog.apply(console, args);

            // Check if this is a progress log
            const logStr = args.join(" ");
            if (logStr.includes("Upload progress:")) {
              // Extract progress percentage
              const match = logStr.match(/(\d+)%/);
              if (match && match[1]) {
                const progress = Number.parseInt(match[1], 10);
                lastProgress = progress;
                lastProgressUpdate = Date.now();

                // Dispatch a custom event with the progress
                globalThis.dispatchEvent(
                  new CustomEvent("upload-progress", {
                    detail: { fileId, progress },
                  })
                );
              }
            }
          };

          try {
            // Call the original upload function with the AbortController
            const result = await uploadLargeFile(file, abortController);

            // Restore console.log
            console.log = originalConsoleLog;

            // Clear the tracking interval
            clearInterval(trackingInterval);

            return result;
          } catch (error) {
            // Restore console.log and clear interval on error
            console.log = originalConsoleLog;
            clearInterval(trackingInterval);
            throw error;
          }
        };

        // Use our tracking wrapper
        const result = await trackingUploadLargeFile(fileToUpload);

        // Remove the event listener
        globalThis.removeEventListener("upload-progress", progressListener);

        // Remove the AbortController from our map
        setAbortControllers((prev) => {
          const newMap = new Map(prev);

          newMap.delete(fileId);
          return newMap;
        });

        if (result.success) {
          setFiles((prev) =>
            prev.map((f) =>
              f.id === fileId
                ? {
                    ...f,
                    status: "completed",
                    progress: 100,
                    percentComplete: 100,
                    url: result.url,
                  }
                : f
            )
          );
          toast.success(`${fileObj.file.name} has been uploaded`);
        } else {
          // Check if it was cancelled
          if (result.error === "Upload was cancelled") {
            setFiles((prev) =>
              prev.map((f) =>
                f.id === fileId
                  ? {
                      ...f,
                      status: "pending", // Reset to pending so user can try again
                      progress: 0,
                      percentComplete: 0,
                    }
                  : f
              )
            );
            toast.info(`Upload of ${fileObj.file.name} was cancelled`);
          } else {
            setFiles((prev) =>
              prev.map((f) =>
                f.id === fileId
                  ? {
                      ...f,
                      status: "error",
                      error: result.error,
                      progress: 0,
                      percentComplete: 0,
                    }
                  : f
              )
            );
            toast.error(`Failed to upload ${fileObj.file.name}`);
          }
        }
      } else {
        // For smaller files, use the regular upload with AbortController
        const result = await uploadToS3(fileToUpload, abortController);

        if (progressInterval) {
          clearInterval(progressInterval);
        }

        // Remove the AbortController from our map
        setAbortControllers((prev) => {
          const newMap = new Map(prev);

          newMap.delete(fileId);
          return newMap;
        });

        if (result.success) {
          setFiles((prev) =>
            prev.map((f) =>
              f.id === fileId
                ? {
                    ...f,
                    status: "completed",
                    progress: 100,
                    percentComplete: 100,
                    url: result.url,
                  }
                : f
            )
          );
          toast.success(`${fileObj.file.name} has been uploaded`);
        } else {
          // Check if it was cancelled
          if (result.error === "Upload was cancelled") {
            setFiles((prev) =>
              prev.map((f) =>
                f.id === fileId
                  ? {
                      ...f,
                      status: "pending", // Reset to pending so user can try again
                      progress: 0,
                      percentComplete: 0,
                    }
                  : f
              )
            );
            toast.info(`Upload of ${fileObj.file.name} was cancelled`);
          } else {
            setFiles((prev) =>
              prev.map((f) =>
                f.id === fileId
                  ? {
                      ...f,
                      status: "error",
                      error: result.error,
                      progress: 0,
                      percentComplete: 0,
                    }
                  : f
              )
            );
            toast.error(`Failed to upload ${fileObj.file.name}`);
          }
        }
      }
    } catch (error) {
      if (progressInterval) {
        clearInterval(progressInterval);
      }

      // If we have a large file, remove the event listener
      if (isLargeFile) {
        globalThis.removeEventListener("upload-progress", progressListener);
      }

      // Remove the AbortController from our map
      setAbortControllers((prev) => {
        const newMap = new Map(prev);

        newMap.delete(fileId);
        return newMap;
      });

      // Check if it was cancelled
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      if (errorMessage.includes("cancelled")) {
        setFiles((prev) =>
          prev.map((f) =>
            f.id === fileId
              ? {
                  ...f,
                  status: "pending", // Reset to pending so user can try again
                  progress: 0,
                  percentComplete: 0,
                }
              : f
          )
        );
        toast.info(`Upload of ${fileObj.file.name} was cancelled`);
      } else {
        setFiles((prev) =>
          prev.map((f) =>
            f.id === fileId
              ? {
                  ...f,
                  status: "error",
                  error: "An unexpected error occurred",
                  progress: 0,
                  percentComplete: 0,
                }
              : f
          )
        );
        toast.error(`Upload error for ${fileObj.file.name}`);
      }
    }
  };

  const handleBatchUpload = async () => {
    setIsUploading(true);
    // Update to include files with "error" status as well
    const filesToUpload = files.filter(
      (f) => f.status === "pending" || f.status === "error"
    );

    try {
      for (const file of filesToUpload) {
        await handleUpload(file.id);
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemove = (fileId: string) => {
    // Get the AbortController for this file if it exists
    const controller = abortControllers.get(fileId);
    if (controller) {
      // Abort the upload
      controller.abort();
      // Remove from the map
      setAbortControllers((prev) => {
        const newMap = new Map(prev);
        newMap.delete(fileId);
        return newMap;
      });
    }

    // Remove the file from the list
    setFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  // Add a state to track AbortControllers
  const [abortControllers, setAbortControllers] = useState<
    Map<string, AbortController>
  >(new Map());

  return (
    <div className="mx-auto w-full max-w-3xl space-y-8">
      {/* Navigation warning dialog */}
      {showNavigationWarning && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-background w-full max-w-md rounded-lg p-6 shadow-xl">
            <h3 className="mb-2 text-lg font-semibold">Confirm Navigation</h3>
            <p className="mb-6">
              You have uploads in progress. Leaving this page will cancel these
              uploads. Are you sure you want to leave?
            </p>
            <div className="flex justify-end space-x-4">
              <Button variant="outline" onClick={cancelNavigation}>
                Stay on Page
              </Button>
              <Button variant="destructive" onClick={confirmNavigation}>
                Leave Page
              </Button>
            </div>
          </div>
        </div>
      )}

      <div
        {...getRootProps()}
        className={cn(
          "rounded-lg border border-dashed transition-colors",
          isDragActive
            ? "border-primary bg-primary/5 dark:bg-primary/10"
            : "bg-background border-gray-300 hover:border-gray-400 dark:border-gray-700 dark:hover:border-gray-600"
        )}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center px-4 py-12">
          <div className="bg-primary mb-4 rounded-full p-3">
            <ArrowUpIcon className="text-primary-foreground h-6 w-6" />
          </div>
          <h3 className="mb-1 text-lg font-semibold">
            Upload data to {APP_NAME}
          </h3>
          <p className="text-muted-foreground mb-4 text-sm">
            Supported formats: CSV, XLSX, PDF, ZIP
          </p>
          <div className="flex gap-3">
            <Button variant="secondary" size="sm">
              Select Files
            </Button>
            <Button variant="default" size="sm">
              Upload
            </Button>
          </div>
        </div>
      </div>

      {files.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-base font-medium">Selected files</h3>
            {hasUploadableFiles(files) && (
              <Button
                onClick={handleBatchUpload}
                disabled={isUploading}
                variant="default"
              >
                {isUploading && (
                  <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                )}
                Confirm Upload
              </Button>
            )}
          </div>
          <div className="space-y-3">
            {files.map((fileObj) => (
              <div
                key={fileObj.id}
                className="bg-background flex items-center justify-between rounded-lg border p-4"
              >
                <div className="flex min-w-0 flex-1 items-center space-x-4">
                  <div className="bg-muted rounded p-2">
                    {getFileIcon(fileObj.file.name)}
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="truncate text-sm font-medium">
                      {fileObj.file.name}
                    </p>
                    <div className="text-muted-foreground flex flex-col gap-1 text-xs">
                      <div className="flex items-center gap-1">
                        <span>{getFileFormat(fileObj.file)}</span>
                        <span>•</span>
                        <span>{formatFileSize(fileObj.file.size)}</span>
                        {fileObj.percentComplete !== undefined &&
                          fileObj.status === "uploading" && (
                            <>
                              <span>•</span>
                              <span>{fileObj.percentComplete}% complete</span>
                            </>
                          )}
                        {fileObj.status === "pending" && (
                          <>
                            <span>•</span>
                            <span className="text-primary">
                              Ready to upload
                            </span>
                          </>
                        )}
                        {fileObj.status === "error" && (
                          <>
                            <span>•</span>
                            <span className="text-red-500">Upload failed</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="ml-4 flex items-center gap-4">
                  {fileObj.status === "uploading" && (
                    <div className="w-32">
                      <Progress value={fileObj.progress} className="h-1" />
                    </div>
                  )}
                  {fileObj.status === "error" && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUpload(fileObj.id);
                      }}
                      className="text-primary hover:text-primary/80"
                    >
                      Retry
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemove(fileObj.id);
                    }}
                    disabled={isUploading}
                    className="text-muted-foreground hover:text-foreground h-8 w-8"
                  >
                    <XIcon className="h-4 w-4" />
                    <span className="sr-only">Remove file</span>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
