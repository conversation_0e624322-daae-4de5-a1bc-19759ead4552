"use client";

import {
  ArrowRightIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ExpandIcon,
  PlusIcon,
  RefreshCcwIcon,
  Trash2Icon,
} from "lucide-react";
import Link from "next/link";
import React, { ChangeEvent, useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Collapsible, CollapsibleContent } from "~/components/ui/collapsible";
import { Input } from "~/components/ui/input";
import { NumberInput } from "~/components/ui/number-input";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { api } from "~/trpc/react";

export type Pool = "insiders" | "outsiders";

export type DetailedEntry = {
  id: string;
  title: string;
  equityPercentage: number;
  tokenPercentage?: number;
};

export interface Allocation {
  id: string;
  name: string;
  amount: number;
  percentage: number;
  color: string;
  pool: Pool;
  isExpanded: boolean;
  detailedEntries: DetailedEntry[];
  isFixed?: boolean;
}

interface AllocationCardProps {
  allocation: Allocation;
  onRemove: (id: string) => void;
  onUpdate: (id: string, field: "amount" | "percentage", value: number) => void;
  onUpdateName: (id: string, name: string) => void;
  onToggleExpansion: (id: string) => void;
  onAddDetailedEntry: (allocationId: string) => void;
  onRemoveDetailedEntry: (allocationId: string, entryId: string) => void;
  onUpdateDetailedEntry: (
    allocationId: string,
    entryId: string,
    field: keyof DetailedEntry,
    value: string | number
  ) => void;
  onSyncEntries: (allocationId: string) => void;
  onDistributeAllocation: (allocationId: string) => void;
}

export default function AllocationCard({
  allocation,
  onRemove,
  onUpdate,
  onUpdateName,
  onToggleExpansion,
  onAddDetailedEntry,
  onRemoveDetailedEntry,
  onUpdateDetailedEntry,
  onSyncEntries,
  onDistributeAllocation,
}: AllocationCardProps) {
  // Derive detailed entries total and discrepancy flag without useEffect
  const detailedTotal = allocation.detailedEntries.reduce(
    (sum, entry) => sum + (entry.equityPercentage || 0),
    0
  );
  const hasDiscrepancy =
    allocation.detailedEntries.length > 0 &&
    Math.abs(detailedTotal - allocation.percentage) > 0;

  // track which entry and field has focus for controlled tooltips
  const [openEntry, setOpenEntry] = useState<{
    id: string;
    field: "equity" | "token";
  } | null>(null);
  const { data: allocations } = api.allocation.getAll.useQuery();

  return (
    <Card className="overflow-hidden">
      <CardContent>
        <div className="grid grid-cols-[auto_1fr_1fr_auto] items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => onToggleExpansion(allocation.id)}
          >
            {allocation.isExpanded ? <ChevronDownIcon /> : <ChevronRightIcon />}
          </Button>
          <div className="flex items-center gap-2">
            <div
              className="flex size-2 items-center justify-center rounded-full"
              style={{ backgroundColor: allocation.color }}
            ></div>
            {allocation.isFixed ? (
              <div className="text-sm font-medium">{allocation.name}</div>
            ) : (
              <Input
                value={allocation.name}
                onChange={(e) => onUpdateName(allocation.id, e.target.value)}
                className="h-8 w-48"
              />
            )}
          </div>

          <div className="flex items-center gap-2">
            <NumberInput
              value={allocation.amount}
              onChange={(value) =>
                onUpdate(allocation.id, "amount", value ?? 0)
              }
              className="h-9"
            />
            <NumberInput
              value={allocation.percentage}
              onChange={(value) =>
                onUpdate(allocation.id, "percentage", value ?? 0)
              }
              suffix="%"
              className="h-9"
            />
          </div>
          {!allocation.isFixed && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => onRemove(allocation.id)}
            >
              <Trash2Icon className="h-4 w-4" />
            </Button>
          )}
        </div>
        <Collapsible open={allocation.isExpanded}>
          <CollapsibleContent>
            <div className="px-1 py-2">
              <div className="mb-3 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <h4 className="text-sm">Detailed Entries</h4>
                  {hasDiscrepancy && (
                    <Badge variant="destructive">
                      Mismatch: {detailedTotal.toFixed(1)}% vs{" "}
                      {allocation.percentage.toFixed(1)}%
                    </Badge>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onSyncEntries(allocation.id)}
                        >
                          Sync Up
                          <RefreshCcwIcon className="size-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Update allocation total based on detailed entries</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onDistributeAllocation(allocation.id)}
                        >
                          Distribute
                          <ExpandIcon className="size-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Distribute allocation evenly to all entries</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>

              {allocation.detailedEntries.length > 0 && (
                <div
                  className="grid space-y-3 gap-x-2"
                  style={{ gridTemplateColumns: "1fr auto auto auto" }}
                >
                  <div className="text-muted-foreground text-sm font-medium">
                    Title
                  </div>
                  <div className="text-muted-foreground text-sm font-medium">
                    Equity %
                  </div>

                  <div className="text-muted-foreground text-sm font-medium">
                    Token %
                  </div>
                  <div></div>

                  {allocation.detailedEntries.map((entry) => {
                    const relatedAllocation = allocations?.find(
                      (a) => a.title === entry.title
                    );
                    // compute recommended range only if multiple entries with distinct values
                    const values =
                      relatedAllocation?.allocations.map(
                        (a) =>
                          Number.parseFloat(
                            a.equityDecimal?.toString() ?? "0"
                          ) * 100
                      ) ?? [];
                    const minVal = Math.min(...values);
                    const maxVal = Math.max(...values);
                    const showRange = values.length > 1 && minVal !== maxVal;
                    const recommendedAllocation = showRange
                      ? `${minVal}% - ${maxVal}%`
                      : `${minVal}%`;

                    // compute recommended token range only if multiple entries with distinct values
                    const tokenValues =
                      relatedAllocation?.allocations.map(
                        (a) =>
                          Number.parseFloat(
                            a.totalTokenDecimal?.toString() ?? "0"
                          ) * 100
                      ) ?? [];
                    const minTokenVal = Math.min(...tokenValues);
                    const maxTokenVal = Math.max(...tokenValues);
                    const showTokenRange =
                      tokenValues.length > 1 && minTokenVal !== maxTokenVal;
                    const recommendedTokenAllocation = showTokenRange
                      ? `${minTokenVal}% - ${maxTokenVal}%`
                      : `${minTokenVal}%`;

                    return (
                      <React.Fragment key={entry.id}>
                        <Input
                          value={entry.title}
                          onChange={(e: ChangeEvent<HTMLInputElement>) =>
                            onUpdateDetailedEntry(
                              allocation.id,
                              entry.id,
                              "title",
                              e.target.value
                            )
                          }
                          className="h-9"
                          placeholder="Title"
                        />

                        {relatedAllocation ? (
                          <TooltipProvider>
                            <Tooltip
                              open={
                                openEntry?.id === entry.id &&
                                openEntry.field === "equity"
                              }
                            >
                              <TooltipTrigger asChild>
                                <NumberInput
                                  value={entry.equityPercentage}
                                  onFocus={() =>
                                    setOpenEntry({
                                      id: entry.id,
                                      field: "equity",
                                    })
                                  }
                                  onBlur={() => setOpenEntry(null)}
                                  onChange={(value) =>
                                    onUpdateDetailedEntry(
                                      allocation.id,
                                      entry.id,
                                      "equityPercentage",
                                      value ?? ""
                                    )
                                  }
                                  className="h-9 w-20"
                                  suffix="%"
                                  decimalScale={1}
                                />
                              </TooltipTrigger>
                              <TooltipContent>
                                <Link
                                  href={`/benchmarks?benchmarkId=${entry.title}`}
                                >
                                  <div className="flex items-center gap-2">
                                    {`Recommended allocation: ${recommendedAllocation}`}
                                    <ArrowRightIcon className="size-4" />
                                  </div>
                                </Link>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ) : (
                          <NumberInput
                            value={entry.equityPercentage}
                            onChange={(value) =>
                              onUpdateDetailedEntry(
                                allocation.id,
                                entry.id,
                                "equityPercentage",
                                value ?? ""
                              )
                            }
                            className="h-9 w-20"
                            suffix="%"
                            decimalScale={10}
                          />
                        )}

                        {relatedAllocation ? (
                          <TooltipProvider>
                            <Tooltip
                              open={
                                openEntry?.id === entry.id &&
                                openEntry.field === "token"
                              }
                            >
                              <TooltipTrigger asChild>
                                <NumberInput
                                  value={entry.tokenPercentage}
                                  onFocus={() =>
                                    setOpenEntry({
                                      id: entry.id,
                                      field: "token",
                                    })
                                  }
                                  onBlur={() => setOpenEntry(null)}
                                  onChange={(value) =>
                                    onUpdateDetailedEntry(
                                      allocation.id,
                                      entry.id,
                                      "tokenPercentage",
                                      value ?? ""
                                    )
                                  }
                                  className="h-9 w-20"
                                  suffix="%"
                                  decimalScale={10}
                                />
                              </TooltipTrigger>
                              <TooltipContent>
                                <Link
                                  href={`/benchmarks?benchmarkId=${entry.title}`}
                                >
                                  <div className="flex items-center gap-2">
                                    {`Recommended token allocation: ${recommendedTokenAllocation}`}
                                    <ArrowRightIcon className="size-4" />
                                  </div>
                                </Link>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ) : (
                          <NumberInput
                            value={entry.tokenPercentage}
                            onChange={(value) =>
                              onUpdateDetailedEntry(
                                allocation.id,
                                entry.id,
                                "tokenPercentage",
                                value ?? ""
                              )
                            }
                            className="h-9 w-20"
                            suffix="%"
                            decimalScale={10}
                          />
                        )}

                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() =>
                            onRemoveDetailedEntry(allocation.id, entry.id)
                          }
                        >
                          <Trash2Icon className="h-4 w-4" />
                        </Button>
                      </React.Fragment>
                    );
                  })}
                </div>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={() => onAddDetailedEntry(allocation.id)}
              >
                <PlusIcon className="h-4 w-4" />
                Add Entry
              </Button>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
