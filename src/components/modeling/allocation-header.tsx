"use client";

import { AlertCircleIcon } from "lucide-react";
import React from "react";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { Allocation } from "./allocation-card";
import AllocationChart from "./allocation-chart";
import AllocationProgress from "./allocation-progress";

interface AllocationHeaderProps {
  allocations: Allocation[];
}

export function formatNumber(num: number): string {
  return new Intl.NumberFormat().format(num);
}

export default function AllocationHeader({
  allocations,
}: AllocationHeaderProps) {
  const suggestions = allocationSuggestions(allocations);
  return (
    <div className="space-y-4">
      <AllocationProgress />
      <AllocationChart allocations={allocations} />
      {suggestions.length > 0 && (
        <Alert>
          <AlertCircleIcon className="h-4 w-4" />
          {suggestions.map((s) => (
            <React.Fragment key={s.name}>
              <AlertTitle>{s.name}</AlertTitle>
              <AlertDescription>{s.message}</AlertDescription>
            </React.Fragment>
          ))}
        </Alert>
      )}
    </div>
  );
}

function allocationSuggestions(allocations: Allocation[]) {
  const recommendations = [];

  const total = allocations.reduce(
    (acc, curr) => acc + Number(curr.percentage),
    0
  );
  if (total > 100) {
    recommendations.push({
      name: "Total Allocation Exceeds Supply",
      message:
        "Allocated amounts exceed the total supply. Please reduce allocations to match the available supply.",
    });
  }

  for (const allocation of allocations) {
    if (allocation.name === "Investors" && allocation.percentage > 40) {
      recommendations.push({
        name: "Investors",
        message:
          "High investor allocation can limit flexibility for future fundraising and employee incentives. Adjust investor shares to maintain a balanced cap table.",
      });
    } else if (allocation.name === "Founders" && allocation.percentage > 50) {
      recommendations.push({
        name: "Founders",
        message:
          "High founder allocation may discourage future investment and raise governance concerns. Consider reducing founder shares to align with best practices.",
      });
    }
  }
  return recommendations;
}
