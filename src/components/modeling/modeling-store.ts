import { create } from "zustand";

interface ModelingStore {
  totalSupply: number | undefined;
  setTotalSupply: (totalSupply: number | undefined) => void;
  allocatedAmount: number;
  setAllocatedAmount: (amount: number) => void;
}

export const useModelingStore = create<ModelingStore>((set) => ({
  totalSupply: 100_000_000,
  allocatedAmount: 0,
  setTotalSupply: (totalSupply: number | undefined) => set({ totalSupply }),
  setAllocatedAmount: (amount: number) => set({ allocatedAmount: amount }),
}));
