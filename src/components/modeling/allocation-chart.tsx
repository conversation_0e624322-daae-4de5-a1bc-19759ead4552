"use client";

import { useMemo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { Allocation } from "./allocation-card";

interface AllocationChartProps {
  allocations: Allocation[];
}

export default function AllocationChart({ allocations }: AllocationChartProps) {
  // Group allocations by pool
  const groupedAllocations = useMemo(() => {
    const insiders = allocations
      .filter((a) => a.pool === "insiders" && a.percentage > 0)
      .sort((a, b) => b.percentage - a.percentage);

    const outsiders = allocations
      .filter((a) => a.pool === "outsiders" && a.percentage > 0)
      .sort((a, b) => b.percentage - a.percentage);

    return { insiders, outsiders };
  }, [allocations]);

  // Calculate total percentages
  const insidersTotal = groupedAllocations.insiders.reduce(
    (sum, a) => sum + a.percentage,
    0
  );
  const outsidersTotal = groupedAllocations.outsiders.reduce(
    (sum, a) => sum + a.percentage,
    0
  );

  return (
    <TooltipProvider>
      <div className="mb-4">
        {/* Main Chart */}
        <div className="flex h-20 w-full overflow-hidden rounded-md md:h-24">
          {/* Insiders Pool */}
          {groupedAllocations.insiders.map((allocation) => (
            <Tooltip key={allocation.id}>
              <TooltipTrigger asChild>
                <div
                  className="flex h-full cursor-pointer flex-col justify-between p-2 text-center text-white"
                  style={{
                    backgroundColor: allocation.color,
                    width: `${allocation.percentage}%`,
                    minWidth: allocation.percentage > 0 ? "40px" : "0",
                  }}
                >
                  <div className="text-sm font-bold md:text-base">
                    {allocation.percentage.toFixed(1)}%
                  </div>
                  <div className="truncate text-xs md:text-sm">
                    {allocation.name}
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top">
                <div className="font-medium">{allocation.name}</div>
                <div className="text-muted-foreground text-xs">
                  {allocation.percentage.toFixed(2)}%
                </div>
              </TooltipContent>
            </Tooltip>
          ))}

          {/* Outsiders Pool */}
          {groupedAllocations.outsiders.map((allocation) => (
            <Tooltip key={allocation.id}>
              <TooltipTrigger asChild>
                <div
                  className="flex h-full cursor-pointer flex-col justify-between p-2 text-center text-white"
                  style={{
                    backgroundColor: allocation.color,
                    width: `${allocation.percentage}%`,
                    minWidth: allocation.percentage > 0 ? "40px" : "0",
                  }}
                >
                  <div className="text-sm font-bold md:text-base">
                    {allocation.percentage.toFixed(1)}%
                  </div>
                  <div className="truncate text-xs md:text-sm">
                    {allocation.name}
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top">
                <div className="font-medium">{allocation.name}</div>
                <div className="text-muted-foreground text-xs">
                  {allocation.percentage.toFixed(2)}%
                </div>
              </TooltipContent>
            </Tooltip>
          ))}
        </div>

        {/* Pool Indicators */}
        <div className="relative mt-2 flex h-6 w-full">
          {/* Insider Pool Indicator */}
          {insidersTotal > 0 && (
            <div
              className="flex items-center justify-center rounded-l-md border border-blue-300 bg-blue-100"
              style={{ width: `${insidersTotal}%` }}
            >
              <div className="flex items-center text-xs font-medium text-blue-700">
                <span>Insiders Pool · {insidersTotal.toFixed(1)}%</span>
              </div>
            </div>
          )}

          {/* Outsider Pool Indicator */}
          {outsidersTotal > 0 && (
            <div
              className="flex items-center justify-center rounded-r-md border border-purple-300 bg-purple-100"
              style={{ width: `${outsidersTotal}%` }}
            >
              <div className="flex items-center text-xs font-medium text-purple-700">
                <span>Outsiders Pool · {outsidersTotal.toFixed(1)}%</span>
              </div>
            </div>
          )}
        </div>

        {/* Legend */}
        <div className="mt-3 flex flex-wrap gap-4">
          {allocations
            .filter((a) => a.percentage > 0)
            .map((allocation) => (
              <div key={allocation.id} className="flex items-center">
                <div
                  className="mr-1 h-3 w-3 rounded-sm"
                  style={{ backgroundColor: allocation.color }}
                ></div>
                <span className="text-muted-foreground text-xs">
                  {allocation.name} · {allocation.percentage.toFixed(1)}%
                </span>
              </div>
            ))}
        </div>
      </div>
    </TooltipProvider>
  );
}
