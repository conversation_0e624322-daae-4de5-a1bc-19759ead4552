"use client";

import { Progress } from "~/components/ui/progress";
import { formatNumber } from "./allocation-header";
import { useModelingStore } from "./modeling-store";

export default function AllocationProgress() {
  const allocatedAmount = useModelingStore((state) => state.allocatedAmount);

  const totalSupply = useModelingStore((state) => state.totalSupply);
  const formattedAllocated = formatNumber(allocatedAmount);
  const formattedTotal = formatNumber(totalSupply ?? 0);
  const remaining = totalSupply ? totalSupply - allocatedAmount : 0;

  const allocatedPercentage = totalSupply
    ? (allocatedAmount / totalSupply) * 100
    : undefined;

  return (
    <div className="mt-6">
      <div className="mb-1 flex justify-between text-sm">
        <span className="font-medium">Initial Supply</span>
        <span>
          {formattedAllocated} of {formattedTotal} allocated
        </span>
      </div>
      {allocatedPercentage && (
        <Progress value={allocatedPercentage} className="h-4" />
      )}
      {allocatedPercentage && (
        <div className="mt-1 flex justify-between text-sm">
          <span className="text-muted-foreground">
            {formatNumber(remaining)} remaining (
            {(100 - allocatedPercentage).toLocaleString(undefined, {
              maximumSignificantDigits: 1,
            })}
            %)
          </span>
          <span className="font-medium">{allocatedPercentage.toFixed(1)}%</span>
        </div>
      )}
    </div>
  );
}
