"use client";

import { PlusIcon } from "lucide-react";
import { Button } from "~/components/ui/button";
import AllocationCard, {
  Allocation,
  DetailedEntry,
  Pool,
} from "./allocation-card";

interface AllocationPoolProps {
  title: string;
  pool: Pool;
  allocations: Allocation[];
  totalPercentage: number;
  onAddAllocation: () => void;
  onRemoveAllocation: (id: string) => void;
  onUpdateAllocation: (
    id: string,
    field: "amount" | "percentage",
    value: number
  ) => void;
  onUpdateAllocationName: (id: string, name: string) => void;
  onToggleExpansion: (id: string) => void;
  onAddDetailedEntry: (allocationId: string) => void;
  onRemoveDetailedEntry: (allocationId: string, entryId: string) => void;
  onUpdateDetailedEntry: (
    allocationId: string,
    entryId: string,
    field: keyof DetailedEntry,
    value: string | number
  ) => void;
  onSyncEntries: (allocationId: string) => void;
  onDistributeAllocation: (allocationId: string) => void;
}

export default function AllocationPool({
  title,
  pool,
  allocations,
  totalPercentage,
  onAddAllocation,
  onRemoveAllocation,
  onUpdateAllocation,
  onUpdateAllocationName,
  onToggleExpansion,
  onAddDetailedEntry,
  onRemoveDetailedEntry,
  onUpdateDetailedEntry,
  onSyncEntries,
  onDistributeAllocation,
}: AllocationPoolProps) {
  const poolColor = pool === "insiders" ? "bg-blue-500" : "bg-purple-500";

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={`h-3 w-3 ${poolColor} rounded-sm`}></div>
          <h3 className="text-xl font-medium">{title}</h3>
        </div>
        <div className="text-sm font-medium">
          Total: {totalPercentage.toFixed(1)}%
        </div>
      </div>

      <div className="space-y-4">
        {allocations.length > 0 ? (
          allocations.map((allocation) => (
            <AllocationCard
              key={allocation.id}
              allocation={allocation}
              onRemove={onRemoveAllocation}
              onUpdate={onUpdateAllocation}
              onUpdateName={onUpdateAllocationName}
              onToggleExpansion={onToggleExpansion}
              onAddDetailedEntry={onAddDetailedEntry}
              onRemoveDetailedEntry={onRemoveDetailedEntry}
              onUpdateDetailedEntry={onUpdateDetailedEntry}
              onSyncEntries={onSyncEntries}
              onDistributeAllocation={onDistributeAllocation}
            />
          ))
        ) : (
          <div className="bg-muted/30 rounded-lg py-8 text-center">
            <p className="text-muted-foreground">No allocations added yet</p>
          </div>
        )}

        <Button variant="outline" className="w-full" onClick={onAddAllocation}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Add {pool === "insiders" ? "Insider" : "Outsider"} Allocation
        </Button>
      </div>
    </div>
  );
}
