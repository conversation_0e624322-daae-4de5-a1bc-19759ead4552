import {
  defaultShouldDehydrateQuery,
  Mutation<PERSON>ache,
  QueryCache,
  QueryClient,
} from "@tanstack/react-query";
import type { TRPCClientError } from "@trpc/client";
import { toast } from "sonner";
import { deserialize, serialize } from "superjson";
import type { AppRouter } from "~/server/api/root";

export const createQueryClient = () =>
  new QueryClient({
    queryCache: new QueryCache({
      onError: (error) => {
        if (globalThis.window !== undefined) {
          const errorMessage =
            (error as TRPCClientError<AppRouter>)?.message ||
            "An unexpected error occurred.";
          toast.error(errorMessage);
        }
      },
    }),
    mutationCache: new MutationCache({
      onError: (error) => {
        if (globalThis.window !== undefined) {
          const errorMessage =
            (error as TRPCClientError<AppRouter>)?.message ||
            "An unexpected error occurred.";
          toast.error(errorMessage);
        }
      },
    }),
    defaultOptions: {
      queries: {
        staleTime: 30 * 1000,
        retry: false,
      },
      mutations: {
        retry: false,
      },
      dehydrate: {
        serializeData: serialize,
        shouldDehydrateQuery: (query) =>
          defaultShouldDehydrateQuery(query) ||
          query.state.status === "pending",
      },
      hydrate: {
        deserializeData: deserialize,
      },
    },
  });
