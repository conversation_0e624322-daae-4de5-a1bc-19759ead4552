import { PrismaClient } from "@prisma/client";
import { env } from "~/env";

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log:
      env.NODE_ENV === "development" ? ["query", "error", "warn"] : ["error"],
    datasources: {
      db: {
        url: env.DATABASE_URL,
      },
    },
  });

if (env.NODE_ENV !== "production") {
  globalForPrisma.prisma = prisma;

  // Gracefully handle hot reload disconnections
  if (globalThis.window === undefined) {
    process.on("beforeExit", async () => {
      await prisma.$disconnect();
    });

    process.on("SIGINT", async () => {
      await prisma.$disconnect();
      process.exit(0);
    });

    process.on("SIGTERM", async () => {
      await prisma.$disconnect();
      process.exit(0);
    });
  }
}

export const db = prisma;
