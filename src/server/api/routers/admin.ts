import { clerkClient } from "@clerk/nextjs/server";
import { IndustryType } from "@prisma/client";
import { z } from "zod";
import { createTRPCRouter, superadminProcedure } from "~/server/api/trpc";
import {
  validatePublicProjectData,
  type ProjectData,
} from "~/server/schemas/project-data";
import { importPublicProjectData } from "~/server/services/project-data-import";

export const adminRouter = createTRPCRouter({
  getUsers: superadminProcedure.query(async () => {
    const client = await clerkClient();
    const users = await client.users.getUserList({
      limit: 100, // TODO: pagination
    });

    return users;
  }),

  getOrganizations: superadminProcedure.query(async () => {
    const client = await clerkClient();
    const organizations = await client.organizations.getOrganizationList({
      limit: 100, // TODO: pagination
    });

    return organizations;
  }),

  generateImpersonationToken: superadminProcedure
    .input(
      z.object({
        userId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const client = await clerkClient();
      const token = await client.actorTokens.create({
        userId: input.userId,
        actor: {
          sub: ctx.auth.userId,
        },
      });

      return token;
    }),

  createOrganizationWithAdmin: superadminProcedure
    .input(
      z.object({
        organizationName: z.string().min(1, "Organization name is required"),
        adminEmail: z.string().email("Valid email is required"),
      })
    )
    .mutation(async ({ input }) => {
      const client = await clerkClient();

      // First create the organization
      const organization = await client.organizations.createOrganization({
        name: input.organizationName,
      });

      const invitation =
        await client.organizations.createOrganizationInvitation({
          organizationId: organization.id,
          emailAddress: input.adminEmail,
          role: "org:admin",
        });

      return {
        organization,
        invitation,
        success: true,
      };
    }),

  uploadProjectData: superadminProcedure
    .input(
      z.object({
        fileName: z.string().min(1, "File name is required"),
        projectName: z.string().min(1, "Project name is required"),
        projectSymbol: z
          .string()
          .min(1, "Project symbol is required")
          .max(10, "Project symbol must be 10 characters or less"),
        jsonData: z.record(z.any()), // Single project data object
        dataType: z
          .enum(["public_project_data", "private_project_data"])
          .optional()
          .default("public_project_data"),
        organizationId: z.string().optional(),
        industryType: z.nativeEnum(IndustryType),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const {
          fileName,
          projectName,
          projectSymbol,
          jsonData,
          dataType,
          organizationId,
          industryType,
        } = input;

        // Log the upload attempt
        console.log(
          `Admin JSON upload: ${fileName} (${dataType}) by user ${ctx.auth.userId}`
        );

        // Basic validation
        if (!jsonData || (Array.isArray(jsonData) && jsonData.length === 0)) {
          throw new Error("JSON data is empty or invalid");
        }

        // Process data based on dataType
        switch (dataType) {
          case "public_project_data": {
            const validationResult = validatePublicProjectData(jsonData);

            if (!validationResult.success) {
              const errorMessages =
                validationResult.error?.issues
                  ?.map((issue) => `${issue.path.join(".")}: ${issue.message}`)
                  .join("; ") || "Unknown validation error";

              throw new Error(`Invalid project data format: ${errorMessages}`);
            }

            const validatedData = validationResult.data as ProjectData;

            // Create audit log entry with validated data
            const result = {
              fileName,
              dataType,
              recordCount: validatedData.data.allocations.length,
              uploadedBy: ctx.auth.userId,
              uploadedAt: new Date(),
              organizationId: organizationId || ctx.auth.orgId,
              preview: JSON.stringify(validatedData).slice(0, 500) + "...",
              coinId: validatedData.data.vesting.coin_id,
              totalAllocations: validatedData.data.allocations.length,
              tgeDate: validatedData.data.vesting.tge_start_date,
            };

            // Handle public project data import
            console.log(
              `Processing public project data for coin ${validatedData.data.vesting.coin_id}:`,
              `${result.recordCount} allocations,`
            );

            // Import the data to the database
            const importResult = await importPublicProjectData(
              validatedData,
              organizationId || ctx.auth.orgId || "",
              ctx.auth.userId,
              fileName,
              projectName,
              projectSymbol,
              industryType
            );

            return {
              success: true,
              message: `Successfully imported ${result.recordCount} allocation(s) from ${fileName}`,
              result: {
                ...result,
                projectId: importResult.project.id,
                summary: importResult.summary,
              },
            };
          }
          case "private_project_data": {
            // TODO: Implement private project data processing
            throw new Error("Private project data upload not yet implemented");
          }
          default: {
            throw new Error("Invalid data type");
          }
        }
      } catch (error) {
        console.error("JSON upload error:", error);
        throw new Error(
          `Failed to process JSON data: ${error instanceof Error ? error.message : "Unknown error"}`
        );
      }
    }),
});
