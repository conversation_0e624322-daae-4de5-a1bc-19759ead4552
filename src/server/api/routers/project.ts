import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";

export const projectRouter = createTRPCRouter({
  getAll: protectedProcedure.query(async ({ ctx }) => {
    const projects = await db.project.findMany({
      where: {
        organizationId: ctx.orgId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return projects;
  }),
});
