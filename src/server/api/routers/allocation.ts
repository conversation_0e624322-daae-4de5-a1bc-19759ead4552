import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";

export const allocationRouter = createTRPCRouter({
  getAll: protectedProcedure.query(async () => {
    // Fetch all allocations with project data, ordered for grouping
    const allAllocations = await db.allocation.findMany({
      orderBy: [
        { stakeholderType: "asc" },
        { group: "asc" },
        { name: "asc" },
        { createdAt: "asc" }, // Secondary sort within groups
      ],
      include: {
        project: true,
        vestingSchedules: true,
      },
    });

    // Group allocations by stakeholder_type, group, and name
    const groupedAllocations: Record<
      string,
      {
        stakeholder_type: string;
        group: string;
        name: string;
        allocations: (typeof allAllocations)[number][];
      }
    > = {};

    for (const allocation of allAllocations) {
      // Create a unique key for the combination
      const key = `${allocation.stakeholderType}-${allocation.group ?? ""}-${allocation.name ?? ""}`;

      if (!groupedAllocations[key]) {
        groupedAllocations[key] = {
          stakeholder_type: allocation.stakeholderType,
          group: allocation.group ?? "", // Handle potential nulls if applicable
          name: allocation.name ?? "", // Handle potential nulls if applicable,
          allocations: [],
        };
      }
      groupedAllocations[key]?.allocations.push(allocation);
    }

    // Convert the grouped object into an array
    return Object.values(groupedAllocations);
  }),
});
