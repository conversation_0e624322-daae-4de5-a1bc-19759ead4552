import { z } from "zod";
import { INDUSTRIES } from "~/lib/constants";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";

export const dashboardRouter = createTRPCRouter({
  getStatistics: protectedProcedure.query(async () => {
    // Get total count based on filters
    const totalCount = await db.project.count();

    if (totalCount === 0) {
      return {
        totalCount: 0,
        industryDistribution: [],
        teamSizeDistribution: [],
        investorSizeDistribution: [],
      };
    }

    // Get industry distribution
    const industryDistribution = await db.project.groupBy({
      by: ["industry"],
      _count: {
        _all: true,
      },
    });

    // Get team size distribution
    const teamSizeDistribution = await db.project.groupBy({
      by: ["teamSize"],
      _count: {
        _all: true,
      },
    });

    // Get investor size distribution
    const investorSizeDistribution = await db.project.groupBy({
      by: ["investorSize"],
      _count: {
        _all: true,
      },
    });

    // Calculate token supply statistics using Prisma aggregation
    const tokenStats = await db.project.aggregate({
      _count: {
        _all: true,
      },
      _avg: {
        totalTokenSupply: true,
        circulatingTokenSupply: true,
      },
    });

    // Transform the distribution data to include percentages
    const industryDistributionWithPercentage = industryDistribution.map(
      (item) => ({
        industry: item.industry,
        count: item._count._all,
        percentage:
          Math.round(((item._count._all * 100) / totalCount) * 100) / 100,
      })
    );

    const teamSizeDistributionWithPercentage = teamSizeDistribution.map(
      (item) => ({
        teamSize: item.teamSize,
        count: item._count._all,
        percentage:
          Math.round(((item._count._all * 100) / totalCount) * 100) / 100,
      })
    );

    const investorSizeDistributionWithPercentage = investorSizeDistribution.map(
      (item) => ({
        investorSize: item.investorSize,
        count: item._count._all,
        percentage:
          Math.round(((item._count._all * 100) / totalCount) * 100) / 100,
      })
    );

    return {
      totalCount,
      industryDistribution: industryDistributionWithPercentage,
      teamSizeDistribution: teamSizeDistributionWithPercentage,
      investorSizeDistribution: investorSizeDistributionWithPercentage,
      tokenStats,
    };
  }),

  getInsights: protectedProcedure
    .input(
      z.object({
        industry: z
          .enum(INDUSTRIES.map((i) => i.value) as [string, ...string[]])
          .optional(),
        teamSize: z
          .object({
            min: z.number().optional(),
            max: z.number().optional(),
          })
          .optional(),
      })
    )
    .query(async ({ input }) => {
      // Build where conditions for Prisma
      const whereConditions: Record<string, unknown> = {};

      if (input.industry) {
        whereConditions.industry = input.industry;
      }

      if (
        input.teamSize?.min !== undefined ||
        input.teamSize?.max !== undefined
      ) {
        whereConditions.teamSize = {};
        if (input.teamSize.min !== undefined) {
          (whereConditions.teamSize as Record<string, unknown>).gte =
            input.teamSize.min;
        }
        if (input.teamSize.max !== undefined) {
          (whereConditions.teamSize as Record<string, unknown>).lte =
            input.teamSize.max;
        }
      }

      const result = await db.project.aggregate({
        where:
          Object.keys(whereConditions).length > 0 ? whereConditions : undefined,
        _avg: {
          totalTokenSupply: true,
        },
        _max: {
          totalTokenSupply: true,
        },
      });

      // Calculate equity-to-token ratios by stakeholder type
      const investorRatio = await db.allocation.aggregate({
        where: { stakeholderType: "Investor" },
        _avg: {
          equityToTokenRatio: true,
        },
      });

      const teamRatio = await db.allocation.aggregate({
        where: {
          OR: [{ stakeholderType: "Employee" }, { stakeholderType: "Advisor" }],
        },
        _avg: {
          equityToTokenRatio: true,
        },
      });

      return {
        avgTotalTokenSupply: result._avg.totalTokenSupply
          ? Number(result._avg.totalTokenSupply)
          : null,
        maxTotalTokenSupply: result._max.totalTokenSupply
          ? Number(result._max.totalTokenSupply)
          : null,
        investorEquityTokenRatio: investorRatio._avg.equityToTokenRatio
          ? Number(investorRatio._avg.equityToTokenRatio)
          : null,
        teamAndAdvisorEquityTokenRatio: teamRatio._avg.equityToTokenRatio
          ? Number(teamRatio._avg.equityToTokenRatio)
          : null,
      };
    }),
});
