import { clerkClient } from "@clerk/nextjs/server";
import { z } from "zod";
import { USER_TYPES } from "~/lib/constants";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";

export const onboardingRouter = createTRPCRouter({
  completeOnboarding: protectedProcedure
    .input(
      z.object({
        firstName: z.string(),
        lastName: z.string(),
        role: z.enum(USER_TYPES),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { auth } = ctx;
      const client = await clerkClient();

      // Update user with basic info and onboarding completion
      await client.users.updateUser(auth.userId, {
        firstName: input.firstName,
        lastName: input.lastName,
        publicMetadata: {
          onboardingCompleted: true,
          role: input.role,
        },
      });

      return { success: true };
    }),
});
