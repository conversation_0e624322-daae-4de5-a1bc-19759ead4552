import { z } from "zod";

/**
 * Zod schema for project vesting data based on the IVestingData interface
 * and mock data from kata-2.json and anime.json
 */

// Batch schema for individual unlock events
const VestingBatchSchema = z.object({
  date: z.string().datetime(), // ISO 8601 date-time format
  is_tge: z.boolean(), // Whether this is a Token Generation Event
  unlock_percent: z.number().min(0).max(100), // Percentage of tokens unlocked (0-100)
});

// Allocation schema for token allocation categories
const AllocationSchema = z.object({
  name: z.string().min(1, "Allocation name is required"), // e.g., "Team", "Rewards", "Community"
  tokens_percent: z.number().min(0).max(100), // Percentage of total supply (0-100)
  tokens: z.number().int().positive(), // Absolute number of tokens
  unlock_type: z.enum(["linear", "nonlinear"]).nullable(), // Type of unlock schedule
  unlock_frequency_type: z.enum(["day", "week", "month", "year"]).nullable(), // Frequency unit
  unlock_frequency_value: z.number().int().positive().nullable(), // Frequency value (e.g., 1 for every 1 month)
  vesting_duration_type: z.enum(["day", "week", "month", "year"]).nullable(), // Duration unit
  vesting_duration_value: z.number().int().positive().nullable(), // Duration value (e.g., 3 for 3 years)
  round_date: z.string().datetime().nullable(), // Start date for this allocation's vesting
  batches: z.array(VestingBatchSchema), // Array of unlock events
});

// Vesting metadata schema
const VestingSchema = z.object({
  coin_id: z.number().int().positive(), // Unique identifier for the coin/token
  total_start_date: z.string().datetime(), // Overall vesting start date
  tge_start_date: z.string().datetime(), // Token Generation Event start date
  links: z.array(z.string().url()), // Array of URLs to tokenomics documents
  is_hidden: z.boolean(), // Whether this vesting schedule is hidden from public view
  createdAt: z.string().datetime(), // Record creation timestamp
  updatedAt: z.string().datetime(), // Record last update timestamp
});

// Main project data schema
export const ProjectDataSchema = z.object({
  data: z.object({
    vesting: VestingSchema,
    allocations: z
      .array(AllocationSchema)
      .min(1, "At least one allocation is required"),
  }),
});

// Type inference from the schema
export type ProjectData = z.infer<typeof ProjectDataSchema>;
export type VestingData = z.infer<typeof VestingSchema>;
export type AllocationData = z.infer<typeof AllocationSchema>;
export type VestingBatch = z.infer<typeof VestingBatchSchema>;

// Validation helper functions
export const validateProjectData = (data: unknown): ProjectData => {
  return ProjectDataSchema.parse(data);
};

export const validateProjectDataSafe = (data: unknown) => {
  return ProjectDataSchema.safeParse(data);
};

// Schema for validating individual components
export const VestingDataSchema = VestingSchema;
export const AllocationDataSchema = AllocationSchema;
export const VestingBatchDataSchema = VestingBatchSchema;

// Additional validation schemas for specific use cases

// Schema for public project data (might exclude sensitive fields)
export const PublicProjectDataSchema = ProjectDataSchema.extend({
  data: z.object({
    vesting: VestingSchema.omit({ is_hidden: true }), // Remove is_hidden for public data
    allocations: z.array(AllocationSchema).min(1),
  }),
});

// Validation functions for different data types
export const validatePublicProjectData = (data: unknown) => {
  return PublicProjectDataSchema.safeParse(data);
};

// Helper function to validate allocation percentages sum to 100
export const validateAllocationPercentages = (
  allocations: AllocationData[]
): boolean => {
  const totalPercent = allocations.reduce(
    (sum, allocation) => sum + allocation.tokens_percent,
    0
  );
  return Math.abs(totalPercent - 100) < 0.01; // Allow for small floating point errors
};

// Helper function to validate that batch percentages are consistent
export const validateBatchPercentages = (
  allocation: AllocationData
): boolean => {
  if (allocation.batches.length === 0) return true;

  const totalUnlockPercent = allocation.batches.reduce(
    (sum, batch) => sum + batch.unlock_percent,
    0
  );

  // For linear vesting, total should be close to 100%
  // For nonlinear, we allow more flexibility
  if (allocation.unlock_type === "linear") {
    return Math.abs(totalUnlockPercent - 100) < 0.1;
  }

  return totalUnlockPercent <= 100;
};
