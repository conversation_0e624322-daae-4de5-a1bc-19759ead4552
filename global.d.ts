export {};

declare global {
  interface CustomJwtSessionClaims {
    metadata: {
      onboardingCompleted?: boolean;
      userType?: string;
    };
  }

  interface UserPublicMetadata {
    onboardingCompleted?: boolean;
    userType?: string;
  }

  interface UserPrivateMetadata {
    superadmin?: boolean;
  }
}

declare module "lucide-react" {
  export * from "lucide-react/dist/lucide-react.suffixed";
}
