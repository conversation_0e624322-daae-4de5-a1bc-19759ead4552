# Modern Next.js Full-Stack Application

## About The Project

A modern, full-stack web application built with Next.js 15 and TypeScript. This project uses the App Router architecture, tRPC for type-safe APIs, Prisma for database access, and Clerk for authentication. The UI is built with Shadcn components and styled with Tailwind CSS.

## Tech Stack

This project is built with the following technologies:

- **Framework:** [Next.js 15](https://nextjs.org/docs) (App Router)
- **Language:** [TypeScript](https://www.typescriptlang.org/docs/) + [React 19](https://react.dev/)
- **Database/ORM:** [PostgreSQL](https://www.postgresql.org/docs/) + [Prisma](https://www.prisma.io/docs)
- **API:** [tRPC 11](https://trpc.io/docs) with [React Query](https://tanstack.com/query/latest/docs/react/overview)
- **Components/Styling:** [Shadcn UI](https://ui.shadcn.com/) + [Tailwind CSS](https://tailwindcss.com/docs)
- **Package Manager & Runtime:** [Bun](https://bun.sh/docs)
- **Deployment:** [Vercel](https://vercel.com/docs)
- **Linting & Formatting:** [ESLint](https://eslint.org/docs/latest/), [Prettier](https://prettier.io/docs/en/), [Husky](https://typicode.github.io/husky/)
- **Authentication:** [Clerk](https://clerk.com/docs)
- **File/Blob Storage:** [Vercel Blob](https://vercel.com/docs/storage/vercel-blob)
- **State Management:** [React Query](https://tanstack.com/query/latest/docs/react/overview), [NuQs](https://nuqs.47ng.com/) for URL state

## Getting Started

To get a local copy up and running, follow these simple steps.

### Prerequisites

- Node.js (v18.x or later recommended)
- Bun (latest version)
- PostgreSQL database
- Docker (Optional, for local database)

### Installation

1. Clone the repo

   ```sh
   git clone https://github.com/ryanyue123/shika.git
   ```

2. Install dependencies with Bun

   ```sh
   bun install
   ```

3. Set up your environment variables

   ```sh
   cp .env.example .env
   ```

   Then edit the `.env` file with your configuration values, including:

   - Database connection string
   - Clerk API keys
   - Vercel Blob storage credentials

4. Set up the database and run migrations
   ```sh
   bun db:migrate
   ```

### Running the Application

Start the development server with Turbopack for faster builds:

```sh
bun dev
```

The application will be available at http://localhost:3000

### Available Scripts

- `bun build` - Build the application for production
- `bun start` - Start the production server
- `bun check` - Run linting and type checking
- `bun db:generate` - Generate Prisma client
- `bun db:migrate` - Run database migrations
- `bun db:studio` - Open Prisma Studio to manage database
- `bun format:write` - Format code with Prettier
- `bun typecheck` - Run TypeScript type checking

## Project Structure

- `/src/app` - Next.js App Router pages and layouts
- `/src/components` - React components including Shadcn UI
- `/src/lib` - Utility functions and shared code
- `/src/trpc` - tRPC API routes and procedures
- `/prisma` - Database schema and migrations

## Contributing

Contributions are welcome! Please follow these steps:

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request
