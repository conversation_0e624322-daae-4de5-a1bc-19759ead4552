-- CreateEnum
CREATE TYPE "pool_type" AS ENUM ('Employees', 'Founders', 'Investors', 'Advisors', 'Community', 'Treasury', 'Other');

-- CreateEnum
CREATE TYPE "stakeholder_type" AS ENUM ('Advisor', 'Investor', 'Employee', 'Founder', 'Consultant', 'Ex-Employee');

-- CreateEnum
CREATE TYPE "group_type" AS ENUM ('Series A', 'Series B', 'Team', 'Common', 'Founders', 'Advisors', 'Labs Treasury', 'Pre-TGE Consultants', 'Options Available', 'BOD', 'Board Member', 'Advisor');

-- CreateEnum
CREATE TYPE "industry_type" AS ENUM ('AI', 'RWA', 'L1', 'L2', 'DePIN', 'Gaming', 'Marketplace', 'Oracle', 'Other');

-- CreateEnum
CREATE TYPE "vesting_frequency" AS ENUM ('DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'ANNUALLY', 'ONE_TIME');

-- CreateTable
CREATE TABLE "allocation_categories" (
    "id" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "pool_type" "pool_type" NOT NULL,
    "allocation_decimal" DECIMAL(13,12) NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6),

    CONSTRAINT "allocation_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "allocations" (
    "allocation_id" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "category_id" TEXT,
    "stakeholder_type" "stakeholder_type" NOT NULL,
    "group" "group_type" NOT NULL,
    "token_amount" DECIMAL(36,18) NOT NULL,
    "top_up_amount" DECIMAL(36,18),
    "common_shares" DECIMAL(36,18),
    "series_a_preferred_shares" DECIMAL(36,18),
    "series_b_preferred_shares" DECIMAL(36,18),
    "options_and_rsus" DECIMAL(36,18),
    "impact" TEXT,
    "function" TEXT,
    "level" TEXT,
    "tenure_months" INTEGER,
    "location" TEXT,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6),
    "title" TEXT,
    "equity_decimal" DECIMAL(13,12),
    "total_token_decimal_pre_top_ups" DECIMAL(13,12),
    "total_token_decimal" DECIMAL(13,12),
    "equity_to_token_ratio" DECIMAL(20,15),
    "is_generated" BOOLEAN DEFAULT false,

    CONSTRAINT "allocations_pkey" PRIMARY KEY ("allocation_id")
);

-- CreateTable
CREATE TABLE "project" (
    "project_id" TEXT NOT NULL,
    "project_ucid" TEXT,
    "organization_id" TEXT NOT NULL,
    "name" TEXT,
    "website" TEXT,
    "description" TEXT,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6),
    "team_size" INTEGER,
    "investor_size" INTEGER,
    "total_token_supply" BIGINT,
    "circulating_supply" BIGINT,
    "industry" "industry_type",
    "insiderAllocationPoolDecimal" DECIMAL(13,12) NOT NULL DEFAULT 0,
    "outsiderAllocationPoolDecimal" DECIMAL(13,12) NOT NULL DEFAULT 0,

    CONSTRAINT "project_pkey" PRIMARY KEY ("project_id")
);

-- CreateTable
CREATE TABLE "unlock_schedules" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "description" TEXT,
    "lockup_cliff_duration" TEXT NOT NULL,
    "lockup_total_duration" TEXT NOT NULL,
    "tge_date" TIMESTAMPTZ(6),
    "tge_unlock_decimal" DECIMAL(13,10) NOT NULL,
    "unlock_frequency" "vesting_frequency" NOT NULL,
    "unlock_decimal_per_period" DECIMAL(13,10),
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6),
    "projectId" TEXT,

    CONSTRAINT "unlock_schedules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vesting_schedules" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "description" TEXT,
    "cliff_duration" TEXT NOT NULL,
    "vesting_start_date" TIMESTAMPTZ(6) NOT NULL,
    "vesting_end_date" TIMESTAMPTZ(6) NOT NULL,
    "vesting_frequency" "vesting_frequency" NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6),
    "projectId" TEXT,

    CONSTRAINT "vesting_schedules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_AllocationToVestingSchedule" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_AllocationToVestingSchedule_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_AllocationToUnlockSchedule" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_AllocationToUnlockSchedule_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "allocation_categories_project_id_idx" ON "allocation_categories"("project_id");

-- CreateIndex
CREATE INDEX "allocation_categories_pool_type_idx" ON "allocation_categories"("pool_type");

-- CreateIndex
CREATE INDEX "allocations_project_id_idx" ON "allocations"("project_id");

-- CreateIndex
CREATE INDEX "allocations_category_id_idx" ON "allocations"("category_id");

-- CreateIndex
CREATE INDEX "allocations_group_idx" ON "allocations"("group");

-- CreateIndex
CREATE INDEX "allocations_stakeholder_type_idx" ON "allocations"("stakeholder_type");

-- CreateIndex
CREATE INDEX "unlock_schedules_name_idx" ON "unlock_schedules"("name");

-- CreateIndex
CREATE INDEX "vesting_schedules_name_idx" ON "vesting_schedules"("name");

-- CreateIndex
CREATE INDEX "_AllocationToVestingSchedule_B_index" ON "_AllocationToVestingSchedule"("B");

-- CreateIndex
CREATE INDEX "_AllocationToUnlockSchedule_B_index" ON "_AllocationToUnlockSchedule"("B");

-- AddForeignKey
ALTER TABLE "allocation_categories" ADD CONSTRAINT "allocation_categories_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "project"("project_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "allocations" ADD CONSTRAINT "allocations_project_id_project_project_id_fk" FOREIGN KEY ("project_id") REFERENCES "project"("project_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "allocations" ADD CONSTRAINT "allocations_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "allocation_categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "unlock_schedules" ADD CONSTRAINT "unlock_schedules_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "project"("project_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vesting_schedules" ADD CONSTRAINT "vesting_schedules_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "project"("project_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AllocationToVestingSchedule" ADD CONSTRAINT "_AllocationToVestingSchedule_A_fkey" FOREIGN KEY ("A") REFERENCES "allocations"("allocation_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AllocationToVestingSchedule" ADD CONSTRAINT "_AllocationToVestingSchedule_B_fkey" FOREIGN KEY ("B") REFERENCES "vesting_schedules"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AllocationToUnlockSchedule" ADD CONSTRAINT "_AllocationToUnlockSchedule_A_fkey" FOREIGN KEY ("A") REFERENCES "allocations"("allocation_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AllocationToUnlockSchedule" ADD CONSTRAINT "_AllocationToUnlockSchedule_B_fkey" FOREIGN KEY ("B") REFERENCES "unlock_schedules"("id") ON DELETE CASCADE ON UPDATE CASCADE;
