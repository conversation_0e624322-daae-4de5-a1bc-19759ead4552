/*
  Warnings:

  - You are about to drop the `_AllocationToVestingSchedule` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `allocation_categories` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "vesting_type" AS ENUM ('LINEAR', 'NONLINEAR');

-- DropForeignKey
ALTER TABLE "_AllocationToVestingSchedule" DROP CONSTRAINT "_AllocationToVestingSchedule_A_fkey";

-- DropForeignKey
ALTER TABLE "_AllocationToVestingSchedule" DROP CONSTRAINT "_AllocationToVestingSchedule_B_fkey";

-- DropForeignKey
ALTER TABLE "allocation_categories" DROP CONSTRAINT "allocation_categories_project_id_fkey";

-- DropForeignKey
ALTER TABLE "allocations" DROP CONSTRAINT "allocations_category_id_fkey";

-- AlterTable
ALTER TABLE "project" ADD COLUMN     "coin_id" INTEGER,
ADD COLUMN     "tge_start_date" TIMESTAMPTZ(6),
ADD COLUMN     "total_start_date" TIMESTAMPTZ(6),
ALTER COLUMN "insiderAllocationPoolDecimal" DROP NOT NULL,
ALTER COLUMN "outsiderAllocationPoolDecimal" DROP NOT NULL;

-- AlterTable
ALTER TABLE "vesting_schedules" ADD COLUMN     "tge_date" TIMESTAMPTZ(6),
ADD COLUMN     "tge_unlock_decimal" DECIMAL(13,10),
ADD COLUMN     "vesting_type" "vesting_type" NOT NULL DEFAULT 'LINEAR',
ALTER COLUMN "cliff_duration" DROP NOT NULL,
ALTER COLUMN "vesting_start_date" DROP NOT NULL,
ALTER COLUMN "vesting_end_date" DROP NOT NULL,
ALTER COLUMN "vesting_frequency" DROP NOT NULL;

-- DropTable
DROP TABLE "_AllocationToVestingSchedule";

-- DropTable
DROP TABLE "allocation_categories";

-- CreateTable
CREATE TABLE "allocation_pools" (
    "id" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "pool_type" "pool_type" NOT NULL,
    "allocation_decimal" DECIMAL(13,12) NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6),

    CONSTRAINT "allocation_pools_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vesting_milestones" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "description" TEXT,
    "vesting_date" TIMESTAMPTZ(6) NOT NULL,
    "unlock_percentage" DECIMAL(13,10),
    "unlock_amount" DECIMAL(36,18),
    "is_tge" BOOLEAN NOT NULL DEFAULT false,
    "is_cliff" BOOLEAN NOT NULL DEFAULT false,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6),
    "vesting_schedule_id" TEXT NOT NULL,

    CONSTRAINT "vesting_milestones_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_AllocationVestingSchedules" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_AllocationVestingSchedules_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "allocation_pools_project_id_idx" ON "allocation_pools"("project_id");

-- CreateIndex
CREATE INDEX "allocation_pools_pool_type_idx" ON "allocation_pools"("pool_type");

-- CreateIndex
CREATE INDEX "vesting_milestones_vesting_schedule_id_idx" ON "vesting_milestones"("vesting_schedule_id");

-- CreateIndex
CREATE INDEX "vesting_milestones_vesting_date_idx" ON "vesting_milestones"("vesting_date");

-- CreateIndex
CREATE INDEX "vesting_milestones_sort_order_idx" ON "vesting_milestones"("sort_order");

-- CreateIndex
CREATE INDEX "_AllocationVestingSchedules_B_index" ON "_AllocationVestingSchedules"("B");

-- CreateIndex
CREATE INDEX "vesting_schedules_vesting_type_idx" ON "vesting_schedules"("vesting_type");

-- AddForeignKey
ALTER TABLE "allocation_pools" ADD CONSTRAINT "allocation_pools_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "project"("project_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "allocations" ADD CONSTRAINT "allocations_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "allocation_pools"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vesting_milestones" ADD CONSTRAINT "vesting_milestones_vesting_schedule_id_fkey" FOREIGN KEY ("vesting_schedule_id") REFERENCES "vesting_schedules"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AllocationVestingSchedules" ADD CONSTRAINT "_AllocationVestingSchedules_A_fkey" FOREIGN KEY ("A") REFERENCES "allocations"("allocation_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AllocationVestingSchedules" ADD CONSTRAINT "_AllocationVestingSchedules_B_fkey" FOREIGN KEY ("B") REFERENCES "vesting_schedules"("id") ON DELETE CASCADE ON UPDATE CASCADE;
