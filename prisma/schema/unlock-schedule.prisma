model UnlockSchedule {
  id          String  @id @default(cuid())
  name        String?
  description String?

  // Lockup configuration
  lockupCliffDuration String @map("lockup_cliff_duration") // Cliff period in ISO 8601 format (e.g., "P12M", "P1Y", "P18M")
  lockupTotalDuration String @map("lockup_total_duration") // Total lockup duration in ISO 8601 format (e.g., "P4Y", "P48M")

  // TGE configuration
  tgeDate          DateTime? @map("tge_date") @db.Timestamptz(6) // date of TGE
  tgeUnlockDecimal Decimal   @map("tge_unlock_decimal") @db.Decimal(13, 10) // Some 

  unlockFrequency        VestingFrequency @map("unlock_frequency") // How often tokens unlock after cliff
  unlockDecimalPerPeriod Decimal?         @map("unlock_decimal_per_period") @db.Decimal(13, 10) // For linear unlocking (0.0 to 1.0)

  createdAt   DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime?    @updatedAt @map("updated_at") @db.Timestamptz(6)
  allocations Allocation[] // Allocations subject to this unlock schedule
  Project     Project?     @relation(fields: [projectId], references: [id])
  projectId   String?

  @@index([name])
  @@map("unlock_schedules")
}

// // For storing phased unlock milestones (e.g., 0.25 at P6M, 0.25 at P12M, etc.)
// model UnlockMilestone {
//   id               String  @id @default(cuid())
//   durationFromTge  String  @map("duration_from_tge") // When this milestone occurs in ISO 8601 format from TGE (e.g., "P6M", "P1Y")
//   unlockDecimal    Decimal @map("unlock_decimal") @db.Decimal(13, 10) // Decimal of total allocation unlocked at this milestone (0.0 to 1.0)
//   description      String? // Optional description of this milestone

//   createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
//   updatedAt DateTime? @updatedAt @map("updated_at") @db.Timestamptz(6)

//   // Relations
//   unlockSchedule UnlockSchedule @relation(fields: [unlockScheduleId], references: [id], onDelete: Cascade)

//   @@index([unlockScheduleId])
//   @@index([durationFromTge])
//   @@map("unlock_milestones")
// }
