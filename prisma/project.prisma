// Project model and related functionality

model Project {
  id                       String                     @id @default(cuid()) @map("project_id")
  ucid                     String                     @map("project_ucid")
  userId                   String?                    @map("user_id")
  organizationId           String?                    @map("organization_id")
  name                     String?
  website                  String?
  description              String?
  createdAt                DateTime                   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                DateTime?                  @updatedAt @map("updated_at") @db.Timestamptz(6)
  industry                 String?
  teamSize                 Int?                       @map("team_size")
  investorSize             Int?                       @map("investor_size")
  totalTokenSupply         BigInt?                    @map("total_token_supply")
  circulatingTokenSupply   BigInt?                    @map("circulating_supply")
  isGenerated              Boolean?                   @default(false) @map("is_generated")
  
  // Relations
  allocations              Allocation[]
  tokenDistributionSummary TokenDistributionSummary[]
  vestingSchedules         VestingSchedule[]

  @@map("project")
}
