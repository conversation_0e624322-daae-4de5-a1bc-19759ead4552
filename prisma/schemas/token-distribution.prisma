// Token distribution summary model

model TokenDistributionSummary {
  id                String     @id @default(cuid())
  projectId         String     @map("project_id")
  category          category?
  group             GroupType?
  totalTokenPercent Decimal?   @map("total_token_percent") @db.Decimal(20, 15)
  totalTokenAmount  Decimal?   @map("total_token_amount") @db.Decimal(60, 18)
  createdAt         DateTime   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt         DateTime?  @updatedAt @map("updated_at") @db.Timestamptz(6)
  totalTokenDecimal Decimal?   @map("total_token_decimal") @db.Decimal(13, 12)
  
  // Relations
  project           Project    @relation(fields: [projectId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "token_distribution_summary_project_id_project_project_id_fk")

  @@index([category], map: "summary_category_idx")
  @@index([group], map: "summary_group_idx")
  @@index([projectId], map: "summary_project_id_idx")
  @@map("token_distribution_summary")
}
