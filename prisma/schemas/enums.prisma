// Enums for the application

enum StakeholderType {
  Advisor
  Investor
  Employee
  Founder
  Consultant
  Ex_Employee @map("Ex-Employee")

  @@map("stakeholder_type")
}

enum GroupType {
  Series_A            @map("Series A")
  Series_B            @map("Series B")
  Team
  Common
  Founders
  Advisors
  Labs_Treasury       @map("Labs Treasury")
  Pre_TGE_Consultants @map("Pre-TGE Consultants")
  Options_Available   @map("Options Available")
  BOD
  Board_Member        @map("Board Member")
  Advisor

  @@map("group_type")
}

enum VestingFrequency {
  monthly
  one_time
  tge_only
  quarterly

  @@map("vesting_frequency")
}

enum VestingScheduleType {
  public_listing

  @@map("vesting_schedule_type")
}

enum allocation_type {
  absolute
  relative
}

enum category {
  Insider_Pool          @map("Insider Pool")
  Public_Sale           @map("Public Sale")
  Private_Sale          @map("Private Sale")
  Token_Warrant_Holders @map("Token Warrant Holders")
  Foundation_Treasury   @map("Foundation Treasury")
  Ecosystem___Community @map("Ecosystem & Community")
  Team
  Airdrop
  Other
}

enum industry_type {
  AI
  RWA
  L1
  L2
  DePIN
  Gaming
  Marketplace
  Oracle
  Other
}
