// Vesting schedule model for token vesting configuration

model VestingSchedule {
  id                        String               @id @default(cuid())
  projectId                 String               @map("project_id")
  stakeholderType           String               @map("stakeholder_type")
  group                     GroupType
  lockupScheduleStartDate   VestingScheduleType? @map("lookup_schedule_start_date")
  cliffDurationMonths       Int                  @map("cliff_duration_months")
  vestingStartDate          DateTime             @map("vesting_start_date") @db.Timestamptz(6)
  vestingEndDate            DateTime             @map("vesting_end_date") @db.Timestamptz(6)
  vestingFrequency          VestingFrequency     @map("vesting_frequency")
  unlockPercentage          Decimal              @map("unlock_percentage") @db.Decimal(13, 10)
  tgeUnlockPercentage       Decimal              @map("tge_unlock_percentage") @db.Decimal(13, 10)
  vestingScheduleLengthDesc String?              @map("vesting_schedule_length_desc")
  createdAt                 DateTime             @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                 DateTime?            @updatedAt @map("updated_at") @db.Timestamptz(6)
  unlockDecimal             Decimal              @map("unlock_decimal") @db.Decimal(13, 10)
  tgeUnlockDecimal          Decimal              @map("tge_unlock_decimal") @db.Decimal(13, 10)
  
  // Relations
  project                   Project              @relation(fields: [projectId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "vesting_schedules_project_id_project_project_id_fk")
  allocations               Allocation[]

  @@unique([projectId, group])
  @@index([projectId])
  @@index([group])
  @@index([stakeholderType])
  @@map("vesting_schedules")
}
